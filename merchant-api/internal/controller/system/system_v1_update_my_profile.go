package system

import (
	"context"

	"merchant-api/internal/service"

	v1 "merchant-api/api/system/v1"
)

// UpdateMyProfile implements the logic for merchants to update their own profile.
func (c *ControllerV1) UpdateMyProfile(ctx context.Context, req *v1.UpdateMyProfileReq) (res *v1.UpdateMyProfileRes, err error) {
	// 直接调用服务层，服务层会从上下文获取商户ID并验证权限
	res, err = service.SystemServiceInstance.UpdateMyProfile(ctx, req)
	return
}