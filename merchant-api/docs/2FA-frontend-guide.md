# 商户2FA登录流程前端开发指南

## 一、整体流程概览

```mermaid
graph LR
    A[用户点击登录] --> B[Casdoor登录]
    B --> C[后端验证]
    C --> D{检查2FA状态}
    D -->|账户锁定| E[显示锁定提示]
    D -->|未启用2FA| F[显示2FA设置页]
    D -->|已启用2FA| G[显示2FA验证页]
    F --> H[设置成功]
    G --> I[验证成功]
    H --> J[登录完成]
    I --> J
```

## 二、详细API交互说明

### 1. Casdoor登录回调处理

当用户从Casdoor登录成功后，会重定向回前端，URL格式如下：
```
https://yourapp.com/auth/callback?code=xxx&state=yyy
```

**前端需要：**
1. 解析URL参数获取`code`和`state`
2. 调用后端接口进行登录

**API请求：**
```javascript
POST /api/system/auth/casdoor/signin
Content-Type: application/json

{
  "code": "casdoor返回的授权码",
  "state": "casdoor返回的状态值"
}
```

### 2. 登录接口响应处理

后端会根据商户的2FA状态返回不同的响应，前端需要根据响应内容进行相应处理。

#### 响应格式A：账户被锁定
```json
{
  "code": 1016,
  "message": "账户已锁定，请30分钟后重试",
  "data": null
}
```
**前端处理：**
- 显示错误提示
- 可选：显示倒计时
- 禁用登录按钮

#### 响应格式B：需要设置2FA（首次使用）
```json
{
  "code": 0,
  "message": "success",
  "data": {
    "accessToken": {
      "access_token": "xxx",
      "token_type": "Bearer",
      "refresh_token": "xxx",
      "expiry": "2024-01-01T00:00:00Z"
    },
    "requireSetup2FA": true,     // 标识需要设置2FA
    "require2FA": false,
    "totpSecret": "JBSWY3DPEHPK3PXP",  // TOTP密钥（用于手动输入）
    "qrCodeURL": "otpauth://totp/XPay商户平台:merchant_123?secret=JBSWY3DPEHPK3PXP&issuer=XPay商户平台",
    "merchantId": 123
  }
}
```
**前端处理：**
- 保存`merchantId`到sessionStorage
- 显示2FA设置界面
- 展示二维码和密钥

#### 响应格式C：需要验证2FA（已启用用户）
```json
{
  "code": 0,
  "message": "success",
  "data": {
    "accessToken": {
      "access_token": "xxx",
      "token_type": "Bearer",
      "refresh_token": "xxx",
      "expiry": "2024-01-01T00:00:00Z"
    },
    "requireSetup2FA": false,
    "require2FA": true,          // 标识需要验证2FA
    "merchantId": 123
  }
}
```
**前端处理：**
- 保存`merchantId`到sessionStorage
- 显示2FA验证界面

### 3. 2FA设置流程

**UI要求：**
1. 显示二维码（使用qrcode.js等库生成）
2. 显示文本密钥（提供复制功能）
3. 6位验证码输入框
4. 清晰的操作说明

**API请求：**
```javascript
POST /api/auth/2fa/setup
Content-Type: application/json

{
  "code": "123456",        // 用户输入的6位验证码
  "merchantId": 123        // 从sessionStorage获取
}
```

**成功响应：**
```json
{
  "code": 0,
  "message": "success",
  "data": {
    "token": "eyJhbGciOiJIUzI1NiIs...",  // 最终的JWT token
    "expireAt": 1640995200,               // Unix时间戳
    "recoveryCodes": [                    // 10个恢复码
      "a1b2c3d4",
      "e5f6g7h8",
      "i9j0k1l2",
      "m3n4o5p6",
      "q7r8s9t0",
      "u1v2w3x4",
      "y5z6a7b8",
      "c9d0e1f2",
      "g3h4i5j6",
      "k7l8m9n0"
    ]
  }
}
```

**失败响应：**
```json
{
  "code": 1013,
  "message": "2FA验证码无效",
  "data": null
}
```

**前端处理成功响应：**
1. **必须**显示恢复码让用户保存
2. 提供下载/复制功能
3. 用户确认保存后才能继续
4. 存储token到localStorage
5. 跳转到主页面

### 4. 2FA验证流程

**UI要求：**
1. 简洁的6位验证码输入框
2. 可选：显示剩余尝试次数
3. 清晰的错误提示

**API请求：**
```javascript
POST /api/auth/2fa/verify
Content-Type: application/json

{
  "code": "123456",        // 用户输入的6位验证码
  "merchantId": 123        // 从sessionStorage获取
}
```

**成功响应：**
```json
{
  "code": 0,
  "message": "success",
  "data": {
    "token": "eyJhbGciOiJIUzI1NiIs...",  // 最终的JWT token
    "expireAt": 1640995200               // Unix时间戳
  }
}
```

**失败响应（验证码错误）：**
```json
{
  "code": 1013,
  "message": "2FA验证码无效",
  "data": null
}
```

**失败响应（账户锁定）：**
```json
{
  "code": 1016,
  "message": "2FA验证失败次数过多，请稍后重试",
  "data": null
}
```

## 三、完整前端实现示例

### 1. 主要流程处理

```javascript
// utils/auth.js
import { message } from 'antd';
import api from './api';

// 处理Casdoor登录回调
export const handleCasdoorCallback = async () => {
  const urlParams = new URLSearchParams(window.location.search);
  const code = urlParams.get('code');
  const state = urlParams.get('state');
  
  if (!code || !state) {
    message.error('登录参数错误');
    window.location.href = '/login';
    return;
  }
  
  try {
    const response = await api.post('/api/system/auth/casdoor/signin', {
      code,
      state
    });
    
    if (response.code === 0) {
      const { data } = response;
      
      // 保存merchantId供后续使用
      sessionStorage.setItem('merchantId', data.merchantId);
      
      if (data.requireSetup2FA) {
        // 需要设置2FA
        return {
          type: 'SETUP_2FA',
          data: {
            qrCodeURL: data.qrCodeURL,
            totpSecret: data.totpSecret,
            merchantId: data.merchantId
          }
        };
      } else if (data.require2FA) {
        // 需要验证2FA
        return {
          type: 'VERIFY_2FA',
          data: {
            merchantId: data.merchantId
          }
        };
      } else {
        // 直接登录成功（理论上不会发生）
        handleLoginSuccess(data.accessToken.access_token, data.expireAt);
      }
    } else {
      // 处理错误
      handleAuthError(response);
    }
  } catch (error) {
    message.error('网络错误，请重试');
    setTimeout(() => {
      window.location.href = '/login';
    }, 2000);
  }
};

// 处理2FA设置
export const setup2FA = async (code) => {
  const merchantId = sessionStorage.getItem('merchantId');
  
  try {
    const response = await api.post('/api/auth/2fa/setup', {
      code,
      merchantId: parseInt(merchantId)
    });
    
    if (response.code === 0) {
      return {
        success: true,
        token: response.data.token,
        expireAt: response.data.expireAt,
        recoveryCodes: response.data.recoveryCodes
      };
    } else {
      return {
        success: false,
        error: response.message
      };
    }
  } catch (error) {
    return {
      success: false,
      error: '网络错误，请重试'
    };
  }
};

// 处理2FA验证
export const verify2FA = async (code) => {
  const merchantId = sessionStorage.getItem('merchantId');
  
  try {
    const response = await api.post('/api/auth/2fa/verify', {
      code,
      merchantId: parseInt(merchantId)
    });
    
    if (response.code === 0) {
      handleLoginSuccess(response.data.token, response.data.expireAt);
      return { success: true };
    } else {
      if (response.code === 1016) {
        // 账户锁定
        return {
          success: false,
          error: response.message,
          locked: true
        };
      }
      return {
        success: false,
        error: response.message
      };
    }
  } catch (error) {
    return {
      success: false,
      error: '网络错误，请重试'
    };
  }
};

// 处理登录成功
const handleLoginSuccess = (token, expireAt) => {
  localStorage.setItem('authToken', token);
  localStorage.setItem('tokenExpireAt', expireAt);
  sessionStorage.removeItem('merchantId');
  window.location.href = '/dashboard';
};

// 处理认证错误
const handleAuthError = (response) => {
  switch (response.code) {
    case 1016:
      message.error('账户已锁定，请稍后重试');
      break;
    case 1015:
      message.error('登录超时，请重新登录');
      break;
    default:
      message.error(response.message || '登录失败');
  }
  
  setTimeout(() => {
    window.location.href = '/login';
  }, 2000);
};
```

### 2. React组件示例

```jsx
// components/TwoFactorSetup.jsx
import React, { useState } from 'react';
import { Modal, Input, Button, message, Alert, Space, Typography } from 'antd';
import { CopyOutlined } from '@ant-design/icons';
import QRCode from 'qrcode.react';

const { Text, Title, Paragraph } = Typography;

export const TwoFactorSetup = ({ visible, qrCodeURL, totpSecret, onSuccess }) => {
  const [code, setCode] = useState('');
  const [loading, setLoading] = useState(false);
  
  const handleSubmit = async () => {
    if (code.length !== 6) {
      message.error('请输入6位验证码');
      return;
    }
    
    setLoading(true);
    const result = await setup2FA(code);
    setLoading(false);
    
    if (result.success) {
      onSuccess(result);
    } else {
      message.error(result.error);
      setCode('');
    }
  };
  
  const copyToClipboard = () => {
    navigator.clipboard.writeText(totpSecret);
    message.success('密钥已复制');
  };
  
  return (
    <Modal
      title="设置两步验证"
      visible={visible}
      closable={false}
      footer={null}
      width={500}
    >
      <Space direction="vertical" size="large" style={{ width: '100%' }}>
        <Alert
          message="请使用Google Authenticator扫描二维码"
          description="如果无法扫描，请手动输入下方的密钥"
          type="info"
        />
        
        <div style={{ textAlign: 'center' }}>
          <QRCode value={qrCodeURL} size={200} />
        </div>
        
        <div>
          <Text type="secondary">手动输入密钥：</Text>
          <Input.Group compact>
            <Input value={totpSecret} readOnly style={{ width: 'calc(100% - 32px)' }} />
            <Button icon={<CopyOutlined />} onClick={copyToClipboard} />
          </Input.Group>
        </div>
        
        <div>
          <Text>请输入Google Authenticator中显示的6位验证码：</Text>
          <Input
            value={code}
            onChange={e => setCode(e.target.value.replace(/\D/g, '').slice(0, 6))}
            placeholder="000000"
            maxLength={6}
            style={{ marginTop: 8 }}
            onPressEnter={handleSubmit}
          />
        </div>
        
        <Button
          type="primary"
          block
          loading={loading}
          onClick={handleSubmit}
          disabled={code.length !== 6}
        >
          验证并启用
        </Button>
      </Space>
    </Modal>
  );
};

// components/TwoFactorVerify.jsx
export const TwoFactorVerify = ({ visible, onSuccess }) => {
  const [code, setCode] = useState('');
  const [loading, setLoading] = useState(false);
  const [attempts, setAttempts] = useState(0);
  
  const handleSubmit = async () => {
    if (code.length !== 6) {
      message.error('请输入6位验证码');
      return;
    }
    
    setLoading(true);
    const result = await verify2FA(code);
    setLoading(false);
    
    if (result.success) {
      onSuccess();
    } else {
      setCode('');
      setAttempts(prev => prev + 1);
      
      if (result.locked) {
        message.error('账户已锁定，请30分钟后重试');
        setTimeout(() => {
          window.location.href = '/login';
        }, 3000);
      } else {
        message.error(result.error);
        if (attempts >= 3) {
          message.warning(`您还有${5 - attempts - 1}次尝试机会`);
        }
      }
    }
  };
  
  return (
    <Modal
      title="两步验证"
      visible={visible}
      closable={false}
      footer={null}
      width={400}
    >
      <Space direction="vertical" size="large" style={{ width: '100%' }}>
        <Alert
          message="请输入Google Authenticator中的验证码"
          type="info"
        />
        
        <div>
          <Input
            value={code}
            onChange={e => setCode(e.target.value.replace(/\D/g, '').slice(0, 6))}
            placeholder="请输入6位验证码"
            maxLength={6}
            size="large"
            style={{ textAlign: 'center', fontSize: 24, letterSpacing: 8 }}
            onPressEnter={handleSubmit}
            autoFocus
          />
        </div>
        
        <Button
          type="primary"
          block
          size="large"
          loading={loading}
          onClick={handleSubmit}
          disabled={code.length !== 6}
        >
          验证
        </Button>
        
        <div style={{ textAlign: 'center' }}>
          <Text type="secondary">
            遇到问题？
            <a href="#" onClick={e => {
              e.preventDefault();
              message.info('恢复功能暂未开放');
            }}>
              使用恢复码
            </a>
          </Text>
        </div>
      </Space>
    </Modal>
  );
};

// components/RecoveryCodes.jsx
export const RecoveryCodes = ({ visible, codes, onConfirm }) => {
  const [confirmed, setConfirmed] = useState(false);
  
  const downloadCodes = () => {
    const content = codes.join('\n');
    const blob = new Blob([content], { type: 'text/plain' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'recovery-codes.txt';
    a.click();
    URL.revokeObjectURL(url);
  };
  
  const copyCodes = () => {
    navigator.clipboard.writeText(codes.join('\n'));
    message.success('恢复码已复制');
  };
  
  return (
    <Modal
      title="保存恢复码"
      visible={visible}
      closable={false}
      footer={[
        <Button key="download" onClick={downloadCodes}>
          下载
        </Button>,
        <Button key="copy" onClick={copyCodes}>
          复制
        </Button>,
        <Button
          key="confirm"
          type="primary"
          disabled={!confirmed}
          onClick={onConfirm}
        >
          我已保存，继续
        </Button>
      ]}
      width={500}
    >
      <Space direction="vertical" size="large" style={{ width: '100%' }}>
        <Alert
          message="重要提示"
          description="这些恢复码是您在无法使用Google Authenticator时的唯一登录方式。请妥善保存！"
          type="warning"
          showIcon
        />
        
        <div style={{ background: '#f5f5f5', padding: 16, borderRadius: 4 }}>
          {codes.map((code, index) => (
            <div key={index} style={{ fontFamily: 'monospace', marginBottom: 8 }}>
              {index + 1}. {code}
            </div>
          ))}
        </div>
        
        <Checkbox
          checked={confirmed}
          onChange={e => setConfirmed(e.target.checked)}
        >
          我已经安全保存了这些恢复码
        </Checkbox>
      </Space>
    </Modal>
  );
};
```

### 3. 主页面集成

```jsx
// pages/AuthCallback.jsx
import React, { useEffect, useState } from 'react';
import { Spin } from 'antd';
import { handleCasdoorCallback } from '../utils/auth';
import { TwoFactorSetup, TwoFactorVerify, RecoveryCodes } from '../components';

const AuthCallback = () => {
  const [loading, setLoading] = useState(true);
  const [authState, setAuthState] = useState(null);
  const [recoveryCodes, setRecoveryCodes] = useState(null);
  
  useEffect(() => {
    const processCallback = async () => {
      const result = await handleCasdoorCallback();
      setAuthState(result);
      setLoading(false);
    };
    
    processCallback();
  }, []);
  
  const handleSetupSuccess = (result) => {
    setRecoveryCodes(result.recoveryCodes);
  };
  
  const handleRecoveryCodesConfirm = () => {
    // 保存token并跳转
    localStorage.setItem('authToken', result.token);
    localStorage.setItem('tokenExpireAt', result.expireAt);
    window.location.href = '/dashboard';
  };
  
  if (loading) {
    return (
      <div style={{ textAlign: 'center', paddingTop: 100 }}>
        <Spin size="large" tip="正在登录..." />
      </div>
    );
  }
  
  return (
    <>
      {authState?.type === 'SETUP_2FA' && (
        <TwoFactorSetup
          visible={true}
          qrCodeURL={authState.data.qrCodeURL}
          totpSecret={authState.data.totpSecret}
          onSuccess={handleSetupSuccess}
        />
      )}
      
      {authState?.type === 'VERIFY_2FA' && (
        <TwoFactorVerify
          visible={true}
          onSuccess={() => window.location.href = '/dashboard'}
        />
      )}
      
      {recoveryCodes && (
        <RecoveryCodes
          visible={true}
          codes={recoveryCodes}
          onConfirm={handleRecoveryCodesConfirm}
        />
      )}
    </>
  );
};

export default AuthCallback;
```

## 四、错误码速查表

| 错误码 | 含义 | 处理建议 |
|--------|------|----------|
| 0 | 成功 | 继续后续流程 |
| 1012 | 需要进行2FA验证 | 显示2FA验证页面 |
| 1013 | 2FA验证码无效 | 提示错误，清空输入框 |
| 1014 | 需要设置2FA | 显示2FA设置页面 |
| 1015 | 临时认证信息过期 | 提示超时，返回登录页 |
| 1016 | 账户锁定 | 提示锁定，禁用输入 |

## 五、注意事项

### 1. 安全相关
- 不要在前端存储TOTP密钥
- sessionStorage中的merchantId仅用于当前会话
- 登录成功后清理sessionStorage

### 2. 用户体验
- 验证码输入框自动聚焦
- 限制只能输入数字
- 提供清晰的错误提示
- 恢复码必须让用户确认保存

### 3. 兼容性
- 确保二维码库支持移动端
- 剪贴板API需要HTTPS环境
- 考虑不支持剪贴板的浏览器

### 4. 状态管理
- 15分钟缓存时间限制
- 超时需要重新登录
- 防止重复提交请求

## 六、测试要点

1. **正常流程**
   - 新用户设置2FA
   - 老用户验证2FA
   - 恢复码保存流程

2. **异常流程**
   - 验证码错误
   - 连续失败锁定
   - 网络异常处理
   - 缓存超时

3. **边界情况**
   - 快速多次点击
   - 刷新页面
   - 浏览器后退

这份指南应该能帮助前端团队快速理解并实现2FA功能。如有任何疑问，请及时沟通。