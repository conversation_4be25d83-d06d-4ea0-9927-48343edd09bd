package codes

import (
	"github.com/gogf/gf/v2/errors/gcode"
)

// 后台用户管理模块错误码 (3500-3599)
var (
	// CodeMemberNotFound 用户不存在
	CodeMemberNotFound = gcode.New(3501, "用户不存在", nil)
	// CodeUsernameExists 用户名已存在
	CodeUsernameExists = gcode.New(3502, "用户名已存在", nil)
	// CodeEmailExists 邮箱已存在
	CodeEmailExists = gcode.New(3503, "邮箱已存在", nil)
	// CodeMobileExists 手机号已存在
	CodeMobileExists = gcode.New(3504, "手机号已存在", nil)
	// CodeInviteCodeExists 邀请码已存在 (如果邀请码需要唯一)
	CodeInviteCodeExists = gcode.New(3505, "邀请码已存在", nil)
	// CodePasswordError 密码错误 (例如旧密码验证失败)
	CodePasswordError = gcode.New(3506, "密码错误", nil)
	// CodeMemberHasSubordinates 用户存在下级，无法删除
	CodeMemberHasSubordinates = gcode.New(3507, "用户存在下级，无法删除", nil)
	// CodeInvalidParentMember 指定的上级用户无效或不存在
	CodeInvalidParentMember = gcode.New(3508, "指定的上级用户无效或不存在", nil)
	// CodeCannotSetParentToChild 不能将上级设置为自身或其子孙
	CodeCannotSetParentToChild = gcode.New(3509, "不能将上级设置为自身或其子孙", nil)
	// CodePrimaryRoleNotInRoles 主角色必须在关联的角色列表中
	CodePrimaryRoleNotInRoles = gcode.New(3510, "设置的主角色必须在关联的角色列表中", nil)
	// CodeEmailCannotBeEmpty 邮箱不能为空
	CodeEmailCannotBeEmpty = gcode.New(3511, "邮箱不能为空", nil)
)
