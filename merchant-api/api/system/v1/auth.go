package v1

import (
	// "merchant-api/internal/library/captcha"

	"github.com/casdoor/casdoor-go-sdk/casdoorsdk"
	"github.com/gogf/gf/v2/frame/g"

	// "github.com/gogf/gf/v2/os/gtime"
	"golang.org/x/oauth2"
)

// --- 获取管理员信息 ---

// GetAdminInfoReq defines the request structure for getting admin information.
type GetAdminInfoReq struct {
	g.Meta `path:"/admin-info" method:"get" tags:"SystemAuth" summary:"获取管理员信息"`
}

// GetAdminInfoRes defines the response structure containing the admin information.
type GetAdminInfoRes struct {
	Id string `json:"id"                              dc:"管理员ID"` // 管理员ID
	// RoleId   int64  `json:"roleId"                          dc:"角色ID"`    // 角色ID
	RealName string `json:"realName"                        dc:"真实姓名"` // 真实姓名
	Username string `json:"username"                        dc:"帐号"`   // 帐号
	Avatar   string `json:"avatar"                          dc:"头像"`   // 头像
	Email    string `json:"email"                           dc:"邮箱"`   // 邮箱
	// Mobile   string `json:"mobile"                          dc:"手机号码"`    // 手机号码
	// Pid      int64  `json:"pid"                             dc:"上级管理员ID"` // 上级管理员ID
	// Level        int         `json:"level"                           dc:"关系树等级"`   // 关系树等级
	// Tree         string      `json:"tree"                            dc:"关系树"`     // 关系树
	// InviteCode   string      `json:"inviteCode"                      dc:"邀请码"`    // 邀请码
	// LastActiveAt *gtime.Time `json:"lastActiveAt"                    dc:"最后活跃时间"` // 最后活跃时间
	// Remark       string      `json:"remark"                          dc:"备注"`     // 备注
	// Status       int         `json:"status"                          dc:"状态"`     // 状态
	// CreatedAt    *gtime.Time `json:"createdAt"                       dc:"创建时间"`   // 创建时间
	// UpdatedAt    *gtime.Time `json:"updatedAt"                       dc:"修改时间"`   // 修改时间

	// 新增字段
	// RoleName  *string   `json:"roleName,omitempty"              dc:"角色名称"` // 角色名称
	// PostNames *[]string `json:"postNames,omitempty"             dc:"岗位名称"` // 岗位名称列表
	// Permissions *[]string `json:"permissions,omitempty"           dc:"权限列表"` // 权限列表 todo 下个月实现
	Menus []*MenuTreeNode `json:"menus"` // 顶层菜单项列表

	// Merchant specific fields
	MerchantId   uint   `json:"merchantId"   dc:"商户ID"`  // 商户ID
	MerchantName string `json:"merchantName" dc:"商户名称"`  // 商户名称
	BusinessName string `json:"businessName" dc:"业务名称"`  // 业务名称
	WebsiteUrl   string `json:"websiteUrl"   dc:"网站URL"` // 网站URL
	Status       int    `json:"status"       dc:"商户状态"`  // 商户状态
}

// --- 修改个人信息 ---

// UpdateAdminInfoReq defines the request structure for updating admin information.
type UpdateAdminInfoReq struct {
	// g.Meta   `path:"/admin-info" method:"put" tags:"SystemAuth" summary:"修改个人信息"`
	RealName string `json:"realName" v:"length:0,50" dc:"真实姓名"`
	Email    string `json:"email" v:"email" dc:"邮箱"`
	Mobile   string `json:"mobile" v:"integer" dc:"手机号码"`
	Avatar   string `json:"avatar" v:"url" dc:"头像"`
	Password string `json:"password" v:"length:6,32" dc:"密码"`
	Code     string `json:"code" v:"required|length:6,6#验证码不能为空|验证码必须为6位" dc:"2FA验证码"`
}

// UpdateAdminInfoRes defines the response structure after updating admin information.
type UpdateAdminInfoRes struct {
	// Typically empty on success.
}

type CasdoorSigninReq struct {
	g.Meta `path:"/auth/casdoor/signin" method:"post" tags:"SystemAuth" summary:"Casdoor 单点登录"`
	Code   string `json:"code" v:"required#code不能为空"`
	State  string `json:"state" v:"required#state不能为空"`
}

type CasdoorSigninRes struct {
	oauth2.Token `json:"accessToken"`

	// 2FA相关字段
	RequireSetup2FA bool   `json:"requireSetup2FA"`        // 需要设置2FA
	Require2FA      bool   `json:"require2FA"`             // 需要验证2FA
	TotpSecret      string `json:"totpSecret,omitempty"`   // TOTP密钥(仅首次)
	QRCodeBase64    string `json:"qrCodeBase64,omitempty"` // 二维码Base64数据
	MerchantId      uint   `json:"merchantId"`             // 商户ID
}

type GetCasdoorUserInfoReq struct {
	g.Meta `path:"/auth/casdoor/claims" method:"get" tags:"SystemAuth" summary:"Casdoor 用户信息"`
}

type GetCasdoorUserInfoRes struct {
	casdoorsdk.Claims `json:"claims"`
}

// --- 2FA相关接口 ---

// Setup2FAReq defines the request structure for setting up 2FA.
type Setup2FAReq struct {
	g.Meta     `path:"/auth/2fa/setup" method:"post" tags:"SystemAuth" summary:"设置2FA"`
	Code       string `json:"code" v:"required|length:6,6#验证码不能为空|验证码必须为6位" dc:"6位验证码"`
	MerchantId uint   `json:"merchantId" v:"required#商户ID不能为空" dc:"商户ID"`
}

// Setup2FARes defines the response structure after setting up 2FA.
type Setup2FARes struct {
	Token    string `json:"token" dc:"最终认证token"`
	ExpireAt int64  `json:"expireAt" dc:"过期时间"`
}

// Verify2FAReq defines the request structure for verifying 2FA.
type Verify2FAReq struct {
	g.Meta     `path:"/auth/2fa/verify" method:"post" tags:"SystemAuth" summary:"验证2FA"`
	Code       string `json:"code" v:"required|length:6,6#验证码不能为空|验证码必须为6位" dc:"6位验证码"`
	MerchantId uint   `json:"merchantId" v:"required#商户ID不能为空" dc:"商户ID"`
}

// Verify2FARes defines the response structure after verifying 2FA.
type Verify2FARes struct {
	Token    string `json:"token" dc:"最终认证token"`
	ExpireAt int64  `json:"expireAt" dc:"过期时间"`
}
