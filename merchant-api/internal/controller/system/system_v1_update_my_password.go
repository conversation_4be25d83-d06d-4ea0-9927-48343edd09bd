package system

import (
	"context"

	"merchant-api/internal/service"

	v1 "merchant-api/api/system/v1"
)

// UpdateMyPassword implements the logic for merchants to update their own password.
func (c *ControllerV1) UpdateMyPassword(ctx context.Context, req *v1.UpdateMyPasswordReq) (res *v1.UpdateMyPasswordRes, err error) {
	// 直接调用服务层，服务层会从上下文获取商户ID并验证权限
	res, err = service.SystemServiceInstance.UpdateMyPassword(ctx, req)
	return
}