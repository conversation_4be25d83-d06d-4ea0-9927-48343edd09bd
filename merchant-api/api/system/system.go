// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package system

import (
	"context"

	"merchant-api/api/system/v1"
)

type ISystemV1 interface {
	CreateMyApiKey(ctx context.Context, req *v1.CreateMyApiKeyReq) (res *v1.CreateMyApiKeyRes, err error)
	GetMyApiKeyList(ctx context.Context, req *v1.GetMyApiKeyListReq) (res *v1.GetMyApiKeyListRes, err error)
	UpdateMyApiKey(ctx context.Context, req *v1.UpdateMyApiKeyReq) (res *v1.UpdateMyApiKeyRes, err error)
	DeleteMyApiKey(ctx context.Context, req *v1.DeleteMyApiKeyReq) (res *v1.DeleteMyApiKeyRes, err error)
	UpdateMyApiKeyIpWhitelist(ctx context.Context, req *v1.UpdateMyApiKeyIpWhitelistReq) (res *v1.UpdateMyApiKeyIpWhitelistRes, err error)
	UpdateMyApiKeyStatus(ctx context.Context, req *v1.UpdateMyApiKeyStatusReq) (res *v1.UpdateMyApiKeyStatusRes, err error)
	GetMyApiKeySecret(ctx context.Context, req *v1.GetMyApiKeySecretReq) (res *v1.GetMyApiKeySecretRes, err error)
	GetAdminInfo(ctx context.Context, req *v1.GetAdminInfoReq) (res *v1.GetAdminInfoRes, err error)
	UpdateAdminInfo(ctx context.Context, req *v1.UpdateAdminInfoReq) (res *v1.UpdateAdminInfoRes, err error)
	CasdoorSignin(ctx context.Context, req *v1.CasdoorSigninReq) (res *v1.CasdoorSigninRes, err error)
	GetCasdoorUserInfo(ctx context.Context, req *v1.GetCasdoorUserInfoReq) (res *v1.GetCasdoorUserInfoRes, err error)
	Setup2FA(ctx context.Context, req *v1.Setup2FAReq) (res *v1.Setup2FARes, err error)
	Verify2FA(ctx context.Context, req *v1.Verify2FAReq) (res *v1.Verify2FARes, err error)
	GetAuthPaymentOrderList(ctx context.Context, req *v1.GetAuthPaymentOrderListReq) (res *v1.GetAuthPaymentOrderListRes, err error)
	GetAuthPaymentOrder(ctx context.Context, req *v1.GetAuthPaymentOrderReq) (res *v1.GetAuthPaymentOrderRes, err error)
	GetMyAuthPaymentOrderList(ctx context.Context, req *v1.GetMyAuthPaymentOrderListReq) (res *v1.GetMyAuthPaymentOrderListRes, err error)
	GetMyAuthPaymentOrder(ctx context.Context, req *v1.GetMyAuthPaymentOrderReq) (res *v1.GetMyAuthPaymentOrderRes, err error)
	RetryAuthPaymentOrderCallback(ctx context.Context, req *v1.RetryAuthPaymentOrderCallbackReq) (res *v1.RetryAuthPaymentOrderCallbackRes, err error)
	GetAuthPaymentOrderStats(ctx context.Context, req *v1.GetAuthPaymentOrderStatsReq) (res *v1.GetAuthPaymentOrderStatsRes, err error)
	TestCallback(ctx context.Context, req *v1.TestCallbackReq) (res *v1.TestCallbackRes, err error)
	GetTokenSymbols(ctx context.Context, req *v1.GetTokenSymbolsReq) (res *v1.GetTokenSymbolsRes, err error)
	GetDashboardStats(ctx context.Context, req *v1.GetDashboardStatsReq) (res *v1.GetDashboardStatsRes, err error)
	GetMyDepositAddresses(ctx context.Context, req *v1.GetMyDepositAddressesReq) (res *v1.GetMyDepositAddressesRes, err error)
	GetMyDepositAddressDetail(ctx context.Context, req *v1.GetMyDepositAddressDetailReq) (res *v1.GetMyDepositAddressDetailRes, err error)
	GetSupportedTokens(ctx context.Context, req *v1.GetSupportedTokensReq) (res *v1.GetSupportedTokensRes, err error)
	GetIpAccessList(ctx context.Context, req *v1.GetIpAccessListReq) (res *v1.GetIpAccessListRes, err error)
	AddIpAccessList(ctx context.Context, req *v1.AddIpAccessListReq) (res *v1.AddIpAccessListRes, err error)
	DeleteIpAccessList(ctx context.Context, req *v1.DeleteIpAccessListReq) (res *v1.DeleteIpAccessListRes, err error)
	PatchIpAccessList(ctx context.Context, req *v1.PatchIpAccessListReq) (res *v1.PatchIpAccessListRes, err error)
	GetLoginLogList(ctx context.Context, req *v1.GetLoginLogListReq) (res *v1.GetLoginLogListRes, err error)
	GetLoginLogDetail(ctx context.Context, req *v1.GetLoginLogDetailReq) (res *v1.GetLoginLogDetailRes, err error)
	GetMenuList(ctx context.Context, req *v1.GetMenuListReq) (res *v1.GetMenuListRes, err error)
	AddMenu(ctx context.Context, req *v1.AddMenuReq) (res *v1.AddMenuRes, err error)
	EditMenu(ctx context.Context, req *v1.EditMenuReq) (res *v1.EditMenuRes, err error)
	DeleteMenu(ctx context.Context, req *v1.DeleteMenuReq) (res *v1.DeleteMenuRes, err error)
	GetMerchantList(ctx context.Context, req *v1.GetMerchantListReq) (res *v1.GetMerchantListRes, err error)
	GetMerchant(ctx context.Context, req *v1.GetMerchantReq) (res *v1.GetMerchantRes, err error)
	AddMerchant(ctx context.Context, req *v1.AddMerchantReq) (res *v1.AddMerchantRes, err error)
	EditMerchant(ctx context.Context, req *v1.EditMerchantReq) (res *v1.EditMerchantRes, err error)
	DeleteMerchant(ctx context.Context, req *v1.DeleteMerchantReq) (res *v1.DeleteMerchantRes, err error)
	UpdateMerchantStatus(ctx context.Context, req *v1.UpdateMerchantStatusReq) (res *v1.UpdateMerchantStatusRes, err error)
	ResetMerchantGoogle2FA(ctx context.Context, req *v1.ResetMerchantGoogle2FAReq) (res *v1.ResetMerchantGoogle2FARes, err error)
	ResetMerchantPassword(ctx context.Context, req *v1.ResetMerchantPasswordReq) (res *v1.ResetMerchantPasswordRes, err error)
	GenerateMerchantApiKey(ctx context.Context, req *v1.GenerateMerchantApiKeyReq) (res *v1.GenerateMerchantApiKeyRes, err error)
	GetMerchantApiKeyList(ctx context.Context, req *v1.GetMerchantApiKeyListReq) (res *v1.GetMerchantApiKeyListRes, err error)
	UpdateMerchantApiKey(ctx context.Context, req *v1.UpdateMerchantApiKeyReq) (res *v1.UpdateMerchantApiKeyRes, err error)
	RevokeMerchantApiKey(ctx context.Context, req *v1.RevokeMerchantApiKeyReq) (res *v1.RevokeMerchantApiKeyRes, err error)
	GetMyProfile(ctx context.Context, req *v1.GetMyProfileReq) (res *v1.GetMyProfileRes, err error)
	UpdateMyProfile(ctx context.Context, req *v1.UpdateMyProfileReq) (res *v1.UpdateMyProfileRes, err error)
	UpdateMyPassword(ctx context.Context, req *v1.UpdateMyPasswordReq) (res *v1.UpdateMyPasswordRes, err error)
	UpdateMyPaymentPassword(ctx context.Context, req *v1.UpdateMyPaymentPasswordReq) (res *v1.UpdateMyPaymentPasswordRes, err error)
	UpdateMyGoogle2FA(ctx context.Context, req *v1.UpdateMyGoogle2FAReq) (res *v1.UpdateMyGoogle2FARes, err error)
	ResetMyGoogle2FA(ctx context.Context, req *v1.ResetMyGoogle2FAReq) (res *v1.ResetMyGoogle2FARes, err error)
	ResetMyWebhookSecret(ctx context.Context, req *v1.ResetMyWebhookSecretReq) (res *v1.ResetMyWebhookSecretRes, err error)
	GetMyWebhookSecret(ctx context.Context, req *v1.GetMyWebhookSecretReq) (res *v1.GetMyWebhookSecretRes, err error)
	GetMyCallbacks(ctx context.Context, req *v1.GetMyCallbacksReq) (res *v1.GetMyCallbacksRes, err error)
	GetMyCallbackDetail(ctx context.Context, req *v1.GetMyCallbackDetailReq) (res *v1.GetMyCallbackDetailRes, err error)
	RetryCallback(ctx context.Context, req *v1.RetryCallbackReq) (res *v1.RetryCallbackRes, err error)
	GetMyDeposits(ctx context.Context, req *v1.GetMyDepositsReq) (res *v1.GetMyDepositsRes, err error)
	GetMyDepositDetail(ctx context.Context, req *v1.GetMyDepositDetailReq) (res *v1.GetMyDepositDetailRes, err error)
	GetMyTransactions(ctx context.Context, req *v1.GetMyTransactionsReq) (res *v1.GetMyTransactionsRes, err error)
	GetMyTransactionDetail(ctx context.Context, req *v1.GetMyTransactionDetailReq) (res *v1.GetMyTransactionDetailRes, err error)
	GetMyTransactionStats(ctx context.Context, req *v1.GetMyTransactionStatsReq) (res *v1.GetMyTransactionStatsRes, err error)
	GetMyWithdraws(ctx context.Context, req *v1.GetMyWithdrawsReq) (res *v1.GetMyWithdrawsRes, err error)
	GetMyWithdrawDetail(ctx context.Context, req *v1.GetMyWithdrawDetailReq) (res *v1.GetMyWithdrawDetailRes, err error)
	CancelMyWithdraw(ctx context.Context, req *v1.CancelMyWithdrawReq) (res *v1.CancelMyWithdrawRes, err error)
	CreateMyWithdraw(ctx context.Context, req *v1.CreateMyWithdrawReq) (res *v1.CreateMyWithdrawRes, err error)
	GetMyWithdrawFee(ctx context.Context, req *v1.GetMyWithdrawFeeReq) (res *v1.GetMyWithdrawFeeRes, err error)
	ApproveWithdraw(ctx context.Context, req *v1.ApproveWithdrawReq) (res *v1.ApproveWithdrawRes, err error)
	RejectWithdraw(ctx context.Context, req *v1.RejectWithdrawReq) (res *v1.RejectWithdrawRes, err error)
	GetOperationLogList(ctx context.Context, req *v1.GetOperationLogListReq) (res *v1.GetOperationLogListRes, err error)
	GetOperationLogDetail(ctx context.Context, req *v1.GetOperationLogDetailReq) (res *v1.GetOperationLogDetailRes, err error)
}
