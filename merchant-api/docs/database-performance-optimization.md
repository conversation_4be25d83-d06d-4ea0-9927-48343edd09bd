# Database Performance Optimization Report

## Overview

This document outlines the performance optimization analysis and recommendations for the agent and telegram information queries that were added to various API endpoints.

## Current Implementation Analysis

### 1. Query Pattern Analysis

All the implemented interfaces now use the following JOIN pattern:

```sql
SELECT [base_fields], first_agent.account as first_agent_name, 
       second_agent.account as second_agent_name, 
       third_agent.account as third_agent_name,
       uba.telegram_id, uba.telegram_username, uba.first_name
FROM [main_table] mt
LEFT JOIN users u ON mt.user_id = u.id
LEFT JOIN users first_agent ON u.first_id = first_agent.id
LEFT JOIN users second_agent ON u.second_id = second_agent.id  
LEFT JOIN users third_agent ON u.third_id = third_agent.id
LEFT JOIN user_backup_accounts uba ON u.id = uba.user_id AND uba.is_master = 1
```

### 2. Performance Impact Assessment

#### Affected Tables and Queries:
- `backup_accounts` - User backup account listings
- `login_logs` - User login history
- `red_packets` - Red packet listings
- `referral_commissions` - Referral commission listings (dual user info)
- `transactions` - Transaction listings
- `user_addresses` - User address listings
- `user_recharges` - User recharge listings
- `user_withdraws` - User withdrawal listings and details
- `wallets` - Wallet listings

#### Query Complexity:
- **Before**: Simple 1-2 table JOINs
- **After**: 5-6 table JOINs per query
- **Additional Overhead**: 4 extra LEFT JOINs per query

## Recommended Database Indexes

### 1. Users Table Indexes

```sql
-- Primary indexes for agent relationships
CREATE INDEX idx_users_first_id ON users(first_id);
CREATE INDEX idx_users_second_id ON users(second_id);
CREATE INDEX idx_users_third_id ON users(third_id);

-- Composite index for common filtering
CREATE INDEX idx_users_account_deleted ON users(account, deleted_at);
CREATE INDEX idx_users_id_deleted ON users(id, deleted_at);

-- Index for account lookups (used in agent name queries)
CREATE INDEX idx_users_account_id ON users(account, id) WHERE deleted_at IS NULL;
```

### 2. User Backup Accounts Table Indexes

```sql
-- Primary index for master account lookups
CREATE INDEX idx_user_backup_accounts_user_master ON user_backup_accounts(user_id, is_master, deleted_at);

-- Index for telegram searches
CREATE INDEX idx_user_backup_accounts_telegram_id ON user_backup_accounts(telegram_id);
CREATE INDEX idx_user_backup_accounts_telegram_username ON user_backup_accounts(telegram_username);
CREATE INDEX idx_user_backup_accounts_first_name ON user_backup_accounts(first_name);
```

### 3. Main Table Indexes

```sql
-- User ID indexes for all main tables
CREATE INDEX idx_user_recharges_user_id ON user_recharges(user_id);
CREATE INDEX idx_user_withdraws_user_id ON user_withdraws(user_id);
CREATE INDEX idx_user_addresses_user_id ON user_addresses(user_id);
CREATE INDEX idx_wallets_user_id ON wallets(user_id);
CREATE INDEX idx_transactions_user_id ON transactions(user_id);
CREATE INDEX idx_red_packets_creator_user_id ON red_packets(creator_user_id);
CREATE INDEX idx_login_logs_user_id ON login_logs(user_id);
CREATE INDEX idx_backup_accounts_user_id ON backup_accounts(user_id);
CREATE INDEX idx_referral_commissions_referrer_id ON referral_commissions(referrer_user_id);
CREATE INDEX idx_referral_commissions_invitee_id ON referral_commissions(invitee_user_id);

-- Composite indexes for common queries with pagination
CREATE INDEX idx_user_recharges_user_created ON user_recharges(user_id, created_at DESC);
CREATE INDEX idx_user_withdraws_user_created ON user_withdraws(user_id, created_at DESC);
CREATE INDEX idx_transactions_user_created ON transactions(user_id, created_at DESC);
```

## Query Optimization Strategies

### 1. Selective Field Loading

Instead of always loading all agent and telegram fields, implement conditional loading:

```go
// Add to utility functions
func GetAgentAndTelegramFieldsSelective(includeAgent, includeTelegram bool) []string {
    var fields []string
    if includeAgent {
        fields = append(fields, 
            "first_agent.account as first_agent_name",
            "second_agent.account as second_agent_name", 
            "third_agent.account as third_agent_name")
    }
    if includeTelegram {
        fields = append(fields,
            "uba.telegram_id",
            "uba.telegram_username", 
            "uba.first_name")
    }
    return fields
}
```

### 2. Caching Strategy

Implement Redis caching for frequently accessed user agent information:

```go
// Cache key pattern: "user:agent_info:{user_id}"
// TTL: 30 minutes
type CachedUserAgentInfo struct {
    FirstAgentName  string `json:"first_agent_name"`
    SecondAgentName string `json:"second_agent_name"`
    ThirdAgentName  string `json:"third_agent_name"`
    TelegramId      string `json:"telegram_id"`
    TelegramUsername string `json:"telegram_username"`
    FirstName       string `json:"first_name"`
}
```

### 3. Batch Loading Optimization

For list queries, implement batch loading to reduce N+1 query problems:

```go
// Instead of JOINing in main query, load user info separately
func LoadUserAgentInfoBatch(ctx context.Context, MerchantIds []uint64) (map[uint64]*UserAgentInfo, error) {
    // Single query to load all user agent info for the batch
    // More efficient than multiple JOINs in main query
}
```

## Performance Monitoring

### 1. Query Performance Metrics

Monitor the following metrics for each endpoint:

- Query execution time (before/after optimization)
- Number of rows examined vs returned
- Index usage statistics
- Cache hit rates

### 2. Recommended Monitoring Queries

```sql
-- Check slow queries
SELECT query_time, sql_text 
FROM mysql.slow_log 
WHERE sql_text LIKE '%first_agent%' 
ORDER BY query_time DESC;

-- Check index usage
SHOW INDEX FROM users;
SHOW INDEX FROM user_backup_accounts;

-- Analyze query execution plans
EXPLAIN SELECT ... FROM users u 
LEFT JOIN users first_agent ON u.first_id = first_agent.id ...;
```

## Implementation Priority

### Phase 1: Critical Indexes (Immediate)
1. `idx_users_first_id`, `idx_users_second_id`, `idx_users_third_id`
2. `idx_user_backup_accounts_user_master`
3. Main table user_id indexes

### Phase 2: Performance Indexes (Week 1)
1. Composite indexes for common query patterns
2. Telegram search indexes
3. Account lookup optimizations

### Phase 3: Advanced Optimizations (Week 2)
1. Implement selective field loading
2. Add Redis caching layer
3. Batch loading optimizations

## Expected Performance Improvements

- **Query Time Reduction**: 60-80% improvement with proper indexes
- **Database Load**: 40-50% reduction in examined rows
- **Cache Hit Rate**: 80-90% for frequently accessed user info
- **Overall Response Time**: 30-50% improvement for list endpoints

## Monitoring and Maintenance

1. **Weekly Performance Reviews**: Monitor slow query logs
2. **Monthly Index Analysis**: Check index usage and effectiveness  
3. **Quarterly Optimization**: Review and update caching strategies
4. **Load Testing**: Regular performance testing under realistic loads

## Risk Assessment

### Low Risk
- Adding recommended indexes (non-blocking operations)
- Implementing caching layer

### Medium Risk  
- Modifying existing query patterns
- Batch loading implementation changes

### High Risk
- Major schema changes
- Removing existing indexes without proper testing

## Implementation Instructions

### 1. Apply Database Indexes

```bash
# Navigate to project root
cd /home/<USER>/admin/merchant-api

# Apply the migration (if migrate CLI is available)
./migrate up

# Or apply manually using MySQL client
mysql -u root -p xpayapi < migrations/mysql/000001_add_agent_telegram_performance_indexes.up.sql
```

### 2. Run Performance Benchmark

```bash
# Before applying indexes
mysql -u root -p xpayapi < scripts/performance_benchmark.sql > benchmark_before.txt

# After applying indexes
mysql -u root -p xpayapi < scripts/performance_benchmark.sql > benchmark_after.txt

# Compare results
diff benchmark_before.txt benchmark_after.txt
```

### 3. Monitor Query Performance

```sql
-- Check slow queries related to agent/telegram info
SELECT query_time, sql_text
FROM mysql.slow_log
WHERE sql_text LIKE '%first_agent%'
   OR sql_text LIKE '%telegram%'
ORDER BY query_time DESC
LIMIT 10;

-- Monitor index usage
SELECT
    table_name,
    index_name,
    cardinality,
    non_unique
FROM information_schema.statistics
WHERE table_schema = 'xpayapi'
  AND index_name LIKE 'idx_%agent%'
  OR index_name LIKE 'idx_%telegram%';
```

## Files Created

1. **Migration Files**:
   - `migrations/mysql/000001_add_agent_telegram_performance_indexes.up.sql`
   - `migrations/mysql/000001_add_agent_telegram_performance_indexes.down.sql`

2. **Performance Testing**:
   - `scripts/performance_benchmark.sql`

3. **Documentation**:
   - `docs/database-performance-optimization.md` (this file)

## Next Steps

1. **Immediate (Phase 1)**:
   - Apply the database indexes using the migration files
   - Run performance benchmarks to measure improvement
   - Monitor slow query logs for any remaining issues

2. **Short Term (Phase 2)**:
   - Implement selective field loading in utility functions
   - Add Redis caching for frequently accessed user agent info
   - Optimize batch loading patterns

3. **Long Term (Phase 3)**:
   - Implement comprehensive caching strategy
   - Add performance monitoring dashboards
   - Regular performance reviews and optimizations

## Conclusion

The addition of agent and telegram information significantly increases query complexity but can be optimized effectively with proper indexing and caching strategies. The recommended optimizations should be implemented in phases to minimize risk while maximizing performance gains.
