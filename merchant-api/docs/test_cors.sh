#!/bin/bash

# 测试 CORS 配置

API_URL="http://localhost:7999/api/system/admin-info"
ORIGIN="https://admin-dev.jjpay.co"

echo "Testing CORS configuration..."
echo "API URL: $API_URL"
echo "Origin: $ORIGIN"
echo ""

echo "1. Testing OPTIONS preflight request:"
curl -i -X OPTIONS "$API_URL" \
  -H "Origin: $ORIGIN" \
  -H "Access-Control-Request-Method: GET" \
  -H "Access-Control-Request-Headers: Authorization"

echo ""
echo ""

echo "2. Testing GET request with Origin header:"
curl -i -X GET "$API_URL" \
  -H "Origin: $ORIGIN" \
  -H "Authorization: Bearer test-token"