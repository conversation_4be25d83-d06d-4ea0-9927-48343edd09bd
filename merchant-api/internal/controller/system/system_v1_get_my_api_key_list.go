package system

import (
	"context"

	"merchant-api/internal/service"

	v1 "merchant-api/api/system/v1"
)

// GetMyApiKeyList implements the logic for merchants to get their own API key list.
func (c *ControllerV1) GetMyApiKeyList(ctx context.Context, req *v1.GetMyApiKeyListReq) (res *v1.GetMyApiKeyListRes, err error) {
	// 直接调用服务层，服务层会从上下文获取商户ID
	res, err = service.SystemServiceInstance.GetMyApiKeyList(ctx, req)
	return
}