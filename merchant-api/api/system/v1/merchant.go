package v1

import (
	"merchant-api/api/common"
	"merchant-api/internal/model/entity"

	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
	"github.com/shopspring/decimal"
)

// --- 获取商户列表 ---

// GetMerchantListReq defines the request structure for getting the merchant list.
type GetMerchantListReq struct {
	g.Meta `path:"/merchants" method:"get" tags:"SystemMerchant" summary:"获取商户列表"`
	common.PageRequest
	MerchantName string   `json:"merchantName" dc:"商户名称 (模糊搜索)"`
	BusinessName string   `json:"businessName" dc:"公司/业务注册名称 (模糊搜索)"`
	Status       *bool    `json:"status" dc:"状态 (true-启用, false-禁用)"`
	DateRange    string   `json:"dateRange" dc:"创建时间范围，格式：2025-01-01,2025-01-31"`
	CreatedAt    []string `json:"createdAt" dc:"创建时间范围数组，格式：[开始时间, 结束时间]"`
	Export       bool     `json:"export" dc:"是否导出 (true-导出, false-不导出)"`
}

// MerchantInfoType defines the structure for merchant information in a list.
type MerchantInfoType struct {
	entity.Merchants
}

// GetMerchantListRes defines the response structure for getting the merchant list.
type GetMerchantListRes struct {
	Page common.PageResponse `json:"page" dc:"分页信息"`
	// Data contains the list of merchants.
	Data []*MerchantInfoType `json:"data" dc:"商户列表数据"`
}

// --- 获取商户详情 ---

// GetMerchantReq defines the request structure for getting merchant details.
type GetMerchantReq struct {
	g.Meta     `path:"/merchants/{merchantId}" method:"get" tags:"SystemMerchant" summary:"获取商户详情"`
	MerchantId uint `json:"merchantId" v:"required#商户ID不能为空" dc:"商户ID"`
}

// MerchantDetailType defines the structure for merchant details.
type MerchantDetailType struct {
	entity.Merchants
	// ApiKeys lists the API keys associated with the merchant.
	ApiKeys []*entity.MerchantApiKeys `json:"apiKeys" dc:"API密钥列表"`
}

// GetMerchantRes defines the response structure for getting merchant details.
type GetMerchantRes struct {
	// Data contains the merchant details.
	Data *MerchantDetailType `json:"data" dc:"商户详情数据"`
}

// --- 添加商户 ---

// AddMerchantReq defines the request structure for adding a new merchant.
type AddMerchantReq struct {
	// g.Meta       `path:"/merchants" method:"post" tags:"SystemMerchant" summary:"添加商户"`
	MerchantName string `json:"merchantName" v:"required|length:2,150#商户名称不能为空|商户名称长度必须在2-150之间" dc:"商户名称"`
	BusinessName string `json:"businessName" v:"length:0,255" dc:"公司/业务注册名称 (可选)"`
	WebsiteUrl   string `json:"websiteUrl" v:"length:0,255" dc:"商户网站URL (可选)"`
	Notes        string `json:"notes" v:"length:0,200" dc:"备注"`
}

// AddMerchantRes defines the response structure after adding a new merchant.
type AddMerchantRes struct {
	// MerchantId is the ID of the newly created merchant.
	MerchantId uint `json:"merchantId" dc:"新增的商户ID"`
}

// --- 编辑商户 ---

// EditMerchantReq defines the request structure for editing an existing merchant.
type EditMerchantReq struct {
	// g.Meta       `path:"/merchants/{merchantId}" method:"put" tags:"SystemMerchant" summary:"编辑商户"`
	MerchantId   uint   `json:"merchantId" v:"required#商户ID不能为空" dc:"商户ID"`
	MerchantName string `json:"merchantName" v:"required|length:2,150#商户名称不能为空|商户名称长度必须在2-150之间" dc:"商户名称"`
	BusinessName string `json:"businessName" v:"length:0,255" dc:"公司/业务注册名称 (可选)"`
	WebsiteUrl   string `json:"websiteUrl" v:"length:0,255" dc:"商户网站URL (可选)"`
	Notes        string `json:"notes" v:"length:0,200" dc:"备注"`
}

// EditMerchantRes defines the response structure after editing a merchant.
type EditMerchantRes struct {
	// Success indicates whether the operation was successful.
	Success bool `json:"success" dc:"是否成功"`
}

// --- 删除商户 ---

// DeleteMerchantReq defines the request structure for deleting merchants.
type DeleteMerchantReq struct {
	// g.Meta      `path:"/merchants" method:"delete" tags:"SystemMerchant" summary:"删除商户"`
	MerchantIds []uint `json:"merchantIds" v:"required#商户ID不能为空" dc:"商户ID列表"`
}

// DeleteMerchantRes defines the response structure after deleting merchants.
type DeleteMerchantRes struct {
	// Success indicates whether the operation was successful.
	Success bool `json:"success" dc:"是否成功"`
}

// --- 更新商户状态 ---

// UpdateMerchantStatusReq defines the request structure for updating a merchant's status.
// Consider using PATCH for partial updates.
type UpdateMerchantStatusReq struct {
	// g.Meta     `path:"/merchants/{merchantId}/status" method:"put" tags:"SystemMerchant" summary:"更新商户状态"`
	MerchantId uint   `json:"merchantId" v:"required#商户ID不能为空" dc:"商户ID"`
	Notes      string `json:"notes" v:"length:0,200" dc:"备注"`
	Status     bool   `json:"status" v:"required#状态不能为空" dc:"状态 (true-启用, false-禁用)"`
}

// UpdateMerchantStatusRes defines the response structure after updating a merchant's status.
type UpdateMerchantStatusRes struct {
	// Success indicates whether the operation was successful.
	Success bool `json:"success" dc:"是否成功"`
}

// --- 重置商户Google 2FA ---

// ResetMerchantGoogle2FAReq defines the request structure for resetting a merchant's Google 2FA.
type ResetMerchantGoogle2FAReq struct {
	g.Meta     `path:"/merchants/{merchantId}/google2fa" method:"put" tags:"SystemMerchant" summary:"重置商户Google 2FA"`
	MerchantId uint `json:"merchantId" v:"required#商户ID不能为空" dc:"商户ID"`
}

// ResetMerchantGoogle2FARes defines the response structure after resetting a merchant's Google 2FA.
type ResetMerchantGoogle2FARes struct {
	// Success indicates whether the operation was successful.
	Success bool `json:"success" dc:"是否成功"`
}

// --- 重置商户密码 ---

// ResetMerchantPasswordReq defines the request structure for resetting a merchant's password.
type ResetMerchantPasswordReq struct {
	g.Meta     `path:"/merchants/{merchantId}/password" method:"put" tags:"SystemMerchant" summary:"重置商户密码"`
	MerchantId uint   `json:"merchantId" v:"required#商户ID不能为空" dc:"商户ID"`
	Password   string `json:"password" v:"required|length:6,30#密码不能为空|密码长度必须在6-30之间" dc:"新密码"`
}

// ResetMerchantPasswordRes defines the response structure after resetting a merchant's password.
type ResetMerchantPasswordRes struct {
	// Success indicates whether the operation was successful.
	Success bool `json:"success" dc:"是否成功"`
}

// --- 生成商户API密钥 ---

// GenerateMerchantApiKeyReq defines the request structure for generating a new API key for a merchant.
type GenerateMerchantApiKeyReq struct {
	g.Meta      `path:"/merchants/{merchantId}/apikeys" method:"post" tags:"SystemMerchant" summary:"生成商户API密钥"`
	MerchantId  uint        `json:"merchantId" v:"required#商户ID不能为空" dc:"商户ID"`
	Label       string      `json:"label" v:"length:0,100" dc:"标签/名称"`
	Scopes      string      `json:"scopes"  dc:"授权范围 (逗号分隔)"`
	IpWhitelist string      `json:"ipWhitelist"  dc:"IP白名单 (逗号分隔)"`
	ExpiresAt   *gtime.Time `json:"expiresAt"  dc:"过期时间 (为空表示永不过期)"`
}

// GenerateMerchantApiKeyRes defines the response structure after generating a merchant API key.
type GenerateMerchantApiKeyRes struct {
	// ApiKeyId is the ID of the newly generated API key.
	ApiKeyId uint `json:"apiKeyId" dc:"API密钥ID"`
	// ApiKey is the generated API key.
	ApiKey string `json:"apiKey" dc:"API密钥"`
	// SecretKey is the generated secret key (returned only once upon creation).
	SecretKey string `json:"secretKey" dc:"密钥 (仅在创建时返回一次)"`
}

// --- 获取商户API密钥列表 ---

// GetMerchantApiKeyListReq defines the request structure for getting the API key list for a merchant.
type GetMerchantApiKeyListReq struct {
	g.Meta `path:"/merchants/{merchantId}/apikeys" method:"get" tags:"SystemMerchant" summary:"获取商户API密钥列表"`
	common.PageRequest
	MerchantId uint     `json:"merchantId" v:"required#商户ID不能为空" dc:"商户ID"`
	ApiKey     string   `json:"apiKey" dc:"API密钥 (模糊搜索)"`
	Label      string   `json:"label" dc:"标签/名称 (模糊搜索)"`
	Status     string   `json:"status" dc:"状态 (active-可用, revoked-已撤销, expired-已过期)"`
	DateRange  string   `json:"dateRange" dc:"创建时间范围，格式：2025-01-01,2025-01-31"`
	CreatedAt  []string `json:"createdAt" dc:"创建时间范围数组，格式：[开始时间, 结束时间]"`
	Export     bool     `json:"export" dc:"是否导出 (true-导出, false-不导出)"`
}

// GetMerchantApiKeyListRes defines the response structure for getting the API key list for a merchant.
type GetMerchantApiKeyListRes struct {
	Page common.PageResponse `json:"page" dc:"分页信息"`
	// Data contains the list of API keys.
	Data []*ApiKeyInfoType `json:"data" dc:"API密钥列表数据"`
}

// --- 更新商户API密钥 ---

// UpdateMerchantApiKeyReq defines the request structure for updating a merchant's API key.
type UpdateMerchantApiKeyReq struct {
	g.Meta      `path:"/merchants/{merchantId}/apikeys/{apiKeyId}" method:"put" tags:"SystemMerchant" summary:"更新商户API密钥"`
	MerchantId  uint        `json:"merchantId" v:"required#商户ID不能为空" dc:"商户ID"`
	ApiKeyId    uint        `json:"apiKeyId" v:"required#API密钥ID不能为空" dc:"API密钥ID"`
	Label       string      `json:"label" v:"length:0,100" dc:"标签/名称"`
	Scopes      string      `json:"scopes" dc:"授权范围 (逗号分隔)"`
	IpWhitelist string      `json:"ipWhitelist" dc:"IP白名单 (逗号分隔)"`
	ExpiresAt   *gtime.Time `json:"expiresAt" dc:"过期时间 (为空表示永不过期)"`
}

// UpdateMerchantApiKeyRes defines the response structure for updating a merchant's API key.
type UpdateMerchantApiKeyRes struct {
	// Success indicates whether the operation was successful.
	Success bool `json:"success" dc:"是否成功"`
}

// --- 撤销商户API密钥 ---

// RevokeMerchantApiKeyReq defines the request structure for revoking a merchant's API key.
type RevokeMerchantApiKeyReq struct {
	g.Meta   `path:"/merchants/apikeys/{apiKeyId}" method:"delete" tags:"SystemMerchant" summary:"撤销商户API密钥"`
	ApiKeyId uint `json:"apiKeyId" v:"required#API密钥ID不能为空" dc:"API密钥ID"`
}

// RevokeMerchantApiKeyRes defines the response structure after revoking a merchant's API key.
type RevokeMerchantApiKeyRes struct {
	// Success indicates whether the operation was successful.
	Success bool `json:"success" dc:"是否成功"`
}

// ============= 商户自管理接口 =============
// 以下接口专供商户管理自己的信息，无需在URL中指定merchantId

// --- 获取我的商户信息 ---

// GetMyProfileReq defines the request structure for a merchant to get their own profile.
type GetMyProfileReq struct {
	g.Meta `path:"/my/profile" method:"get" tags:"MerchantProfile" summary:"获取我的商户信息"`
}

// GetMyProfileRes defines the response structure for getting a merchant's own profile.
type GetMyProfileRes struct {
	// Data contains the merchant profile details.
	Data *MyProfileType `json:"data" dc:"商户信息"`
}

// MyProfileType defines the structure for a merchant's own profile information.
type MyProfileType struct {
	MerchantId         uint        `json:"merchantId" dc:"商户ID"`
	MerchantName       string      `json:"merchantName" dc:"商户名称"`
	BusinessName       string      `json:"businessName" dc:"公司/业务注册名称"`
	Email              string      `json:"email" dc:"商户邮箱"`
	EmailVerifiedAt    *gtime.Time `json:"emailVerifiedAt" dc:"邮箱验证时间"`
	PaymentPasswordSet bool        `json:"paymentPasswordSet" dc:"是否设置支付密码"`
	AreaCode           string      `json:"areaCode" dc:"电话区号"`
	Phone              string      `json:"phone" dc:"联系电话"`
	ContactEmail       string      `json:"contactEmail" dc:"备用联系邮箱"`
	WebsiteUrl         string      `json:"websiteUrl" dc:"商户网站URL"`
	Google2FaEnabled   bool        `json:"google2FaEnabled" dc:"Google 2FA是否启用"`
	Language           string      `json:"language" dc:"语言偏好"`
	Timezone           string      `json:"timezone" dc:"时区设置"`
	Avatar             string      `json:"avatar" dc:"头像URL"`
	CallbackUrl        string      `json:"callbackUrl" dc:"回调URL"`
	WebhookSecret      string      `json:"webhookSecret" dc:"Webhook密钥（部分显示）"`
	LastLoginTime      *gtime.Time `json:"lastLoginTime" dc:"最后登录时间"`
	LastLoginIp        string      `json:"lastLoginIp" dc:"最后登录IP"`
	CreatedAt          *gtime.Time `json:"createdAt" dc:"创建时间"`
	UpdatedAt          *gtime.Time `json:"updatedAt" dc:"更新时间"`
	Assets             []AssetInfo `json:"assets" dc:"资产信息"`
}

// --- 更新我的商户信息 ---

// UpdateMyProfileReq defines the request structure for a merchant to update their own profile.
type UpdateMyProfileReq struct {
	g.Meta          `path:"/my/profile" method:"put" tags:"MerchantProfile" summary:"更新我的商户信息"`
	Password        string `json:"password" v:"length:6,30" dc:"新密码"`
	ConfirmPassword string `json:"confirmPassword" v:"length:6,30" dc:"确认密码"`
	Email           string `json:"email" v:"email" dc:"邮箱地址"`
	AreaCode        string `json:"areaCode" v:"length:0,10" dc:"电话区号"`
	Phone           string `json:"phone" v:"length:0,20" dc:"手机号码"`
	CallbackUrl     string `json:"callbackUrl" v:"url" dc:"回调地址"`
	Code            string `json:"code" v:"length:6,6" dc:"2FA验证码"`
}

// UpdateMyProfileRes defines the response structure for updating a merchant's own profile.
type UpdateMyProfileRes struct {
	// Success indicates whether the operation was successful.
	Success bool `json:"success" dc:"是否成功"`
}

// --- 更新我的登录密码 ---

// UpdateMyPasswordReq defines the request structure for a merchant to update their own password.
type UpdateMyPasswordReq struct {
	g.Meta          `path:"/my/password" method:"put" tags:"MerchantProfile" summary:"更新我的登录密码"`
	CurrentPassword string `json:"currentPassword" v:"required|length:6,30#当前密码不能为空|当前密码长度必须在6-30之间" dc:"当前密码"`
	NewPassword     string `json:"newPassword" v:"required|length:6,30#新密码不能为空|新密码长度必须在6-30之间" dc:"新密码"`
}

// UpdateMyPasswordRes defines the response structure for updating a merchant's own password.
type UpdateMyPasswordRes struct {
	// Success indicates whether the operation was successful.
	Success bool `json:"success" dc:"是否成功"`
}

// --- 设置/更新我的支付密码 ---

// UpdateMyPaymentPasswordReq defines the request structure for a merchant to set/update their payment password.
type UpdateMyPaymentPasswordReq struct {
	g.Meta             `path:"/my/payment-password" method:"put" tags:"MerchantProfile" summary:"设置/更新我的支付密码"`
	CurrentPassword    string `json:"currentPassword" dc:"当前支付密码 (首次设置时不需要)"`
	NewPaymentPassword string `json:"newPaymentPassword" v:"required|length:6,30#支付密码不能为空|支付密码长度必须在6-30之间" dc:"新支付密码"`
	LoginPassword      string `json:"loginPassword" v:"required#登录密码不能为空" dc:"登录密码 (用于验证身份)"`
}

// UpdateMyPaymentPasswordRes defines the response structure for updating a merchant's payment password.
type UpdateMyPaymentPasswordRes struct {
	// Success indicates whether the operation was successful.
	Success bool `json:"success" dc:"是否成功"`
}

// --- 启用/禁用我的Google 2FA ---

// UpdateMyGoogle2FAReq defines the request structure for a merchant to enable/disable their Google 2FA.
type UpdateMyGoogle2FAReq struct {
	g.Meta   `path:"/my/google2fa" method:"put" tags:"MerchantProfile" summary:"启用/禁用我的Google 2FA"`
	Enable   bool   `json:"enable" v:"required#操作类型不能为空" dc:"是否启用 (true-启用, false-禁用)"`
	Secret   string `json:"secret" dc:"Google 2FA密钥 (启用时必填)"`
	Code     string `json:"code" v:"required|length:6,6#验证码不能为空|验证码必须为6位" dc:"Google 2FA验证码"`
	Password string `json:"password" v:"required#密码不能为空" dc:"登录密码 (用于验证身份)"`
}

// UpdateMyGoogle2FARes defines the response structure for updating a merchant's Google 2FA status.
type UpdateMyGoogle2FARes struct {
	// Success indicates whether the operation was successful.
	Success bool `json:"success" dc:"是否成功"`
	// Secret is returned when enabling 2FA for the first time.
	Secret string `json:"secret,omitempty" dc:"Google 2FA密钥 (仅在首次启用时返回)"`
	// QRCode is the QR code URL for setting up Google 2FA (only when enabling).
	QRCode string `json:"qrCode,omitempty" dc:"Google 2FA二维码URL (仅在启用时返回)"`
}

// --- 重置我的Google 2FA ---

// ResetMyGoogle2FAReq defines the request structure for a merchant to reset their Google 2FA.
type ResetMyGoogle2FAReq struct {
	g.Meta   `path:"/my/google2fa" method:"delete" tags:"MerchantProfile" summary:"重置我的Google 2FA"`
	Password string `json:"password" v:"required#密码不能为空" dc:"登录密码 (用于验证身份)"`
	Code     string `json:"code" v:"required|length:6,6#验证码不能为空|验证码必须为6位" dc:"当前Google 2FA验证码"`
}

// ResetMyGoogle2FARes defines the response structure for resetting a merchant's Google 2FA.
type ResetMyGoogle2FARes struct {
	// Success indicates whether the operation was successful.
	Success bool `json:"success" dc:"是否成功"`
}

// AssetInfo defines the structure for merchant asset information.
type AssetInfo struct {
	TokenSymbol   string          `json:"tokenSymbol" dc:"代币符号"`
	Available     decimal.Decimal `json:"available" dc:"可用余额"`
	Frozen        decimal.Decimal `json:"frozen" dc:"冻结余额"`
	Total         decimal.Decimal `json:"total" dc:"总余额"`
	DecimalPlaces uint            `json:"decimalPlaces" dc:"小数位数"`
}

// --- 重置我的Webhook密钥 ---

// ResetMyWebhookSecretReq defines the request structure for a merchant to reset their webhook secret.
type ResetMyWebhookSecretReq struct {
	g.Meta   `path:"/my/webhook-secret" method:"put" tags:"MerchantProfile" summary:"重置我的Webhook密钥"`
	Code     string `json:"code" v:"length:6,6" dc:"2FA验证码（如果启用）"`
}

// ResetMyWebhookSecretRes defines the response structure for resetting a merchant's webhook secret.
type ResetMyWebhookSecretRes struct {
	// WebhookSecret is the new webhook secret.
	WebhookSecret string `json:"webhookSecret" dc:"新的Webhook密钥"`
}

// --- 查看我的Webhook密钥 ---

// GetMyWebhookSecretReq defines the request structure for a merchant to view their webhook secret.
type GetMyWebhookSecretReq struct {
	g.Meta `path:"/my/webhook-secret" method:"get" tags:"MerchantProfile" summary:"查看我的Webhook密钥（完整显示）"`
	Code   string `json:"code" v:"length:6,6" dc:"2FA验证码（如果启用）"`
}

// GetMyWebhookSecretRes defines the response structure for viewing a merchant's webhook secret.
type GetMyWebhookSecretRes struct {
	// WebhookSecret is the webhook secret (full secret without masking).
	WebhookSecret string `json:"webhookSecret" dc:"Webhook密钥（完整显示）"`
}
