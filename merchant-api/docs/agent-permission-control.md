# 代理权限控制实现文档

## 概述

本文档描述了XPay Agent API中代理权限控制的实现方案。该系统是一个纯代理后台系统，确保代理只能访问其组织层级下的用户数据。

## 实现原理

### 1. 数据库结构

#### 代理层级结构
- `agents`表中的`relationship`字段存储代理的层级路径
- 格式示例：
  - 一级代理：`/1/`
  - 二级代理：`/1/3/`（ID为3，父级是ID为1）
  - 三级代理：`/1/3/4/`（ID为4，父级是ID为3）

#### 用户关联
- `users`表中的`agent_relationship`字段存储用户所属的代理层级路径
- 与代理的`relationship`字段格式相同

### 2. 认证中间件

在`internal/middleware/auth.go`中：
- 所有登录用户都是代理（从`agents`表认证）
- 代理登录时，将`agentId`存储在请求上下文中

### 3. 权限过滤实现

由于循环依赖问题，权限过滤直接在各个DAO层实现，不使用集中的工具函数。

标准实现模式：
```go
// 添加代理权限过滤
httpReq := g.RequestFromCtx(ctx)
if httpReq != nil {
    agentId := httpReq.GetCtxVar("agentId").Int64()
    
    if agentId > 0 {
        // 获取当前代理的relationship
        var currentAgent *entity.Agents
        err := Agents.Ctx(ctx).
            Where(Agents.Columns().AgentId, agentId).
            WhereNull(Agents.Columns().DeletedAt).
            Scan(&currentAgent)
        
        if err != nil || currentAgent == nil {
            return nil, 0, gerror.NewCode(codes.CodeForbidden, "无权访问数据")
        }
        
        // 只查询agent_relationship以当前代理路径开头的数据
        m = m.WhereLike("u.agent_relationship", currentAgent.Relationship+"%")
    }
}
```

### 4. 接口实现

#### 已实现权限控制的接口：

1. **agents（代理管理）**
   - 文件：`internal/logic/system/v1/agent.go`
   - 方法：`GetAgentList`
   - 权限规则：代理只能看到自己的直接下级代理

2. **users（用户管理）**
   - 文件：`internal/dao/users.go`
   - 方法：`GetUserListWithAgentInfo`
   - 权限规则：代理只能看到其层级下的所有用户

3. **transfers（转账记录）**
   - 文件：`internal/dao/transfers.go`
   - 方法：`GetTransferList`, `GetTransferById`
   - 权限规则：代理能看到发送方或接收方任一方在其路径下的转账记录

4. **payment-requests（支付请求）**
   - 方法：`ListAdminPaymentRequestsWithFullInfo`, `GetPaymentRequestWithDetails`
   - 权限规则：代理能看到发起者或付款人任一方在其路径下的请求

5. **red-packets（红包）**
   - 文件：`internal/dao/red_packets.go`
   - 方法：`ListAdminRedPacketsWithFullInfo`, `GetAdminRedPacketDetail`
   - 权限规则：代理只能看到其路径下用户创建的红包

6. **red-packet-claims（红包领取记录）**
   - 文件：`internal/dao/red_packet_claims.go`
   - 方法：`ListAdminRedPacketClaimsWithFullInfo`
   - 权限规则：代理能看到领取者或发送者任一方在其路径下的记录

7. **red-packet-images（红包图片）**
   - 文件：`internal/dao/red_packet_images.go`
   - 方法：`ListAdminRedPacketImagesWithFullInfo`
   - 权限规则：代理只能看到其路径下用户上传的图片

8. **wallets（钱包）**
   - 文件：`internal/dao/wallets.go`
   - 方法：`ListAdminWalletsWithFullInfo`
   - 权限规则：代理只能看到其路径下用户的钱包

9. **transactions（交易记录）**
   - 文件：`internal/dao/transactions.go`
   - 方法：`ListAdminTransactionsWithFullInfo`
   - 权限规则：代理只能看到其路径下用户的交易记录

10. **user-recharges（用户充值）**
    - 文件：`internal/dao/user_recharges.go`
    - 方法：`ListAdminUserRechargesWithFullInfo`
    - 权限规则：代理只能看到其路径下用户的充值记录

11. **user-addresses（用户地址）**
    - 文件：`internal/dao/user_address.go`
    - 方法：`ListAdminUserAddressesWithFullInfo`
    - 权限规则：代理只能看到其路径下用户的地址

12. **user-withdraws（用户提现）**
    - 文件：`internal/logic/system/v1/user_withdraw/user_withdraw_repo.go`
    - 方法：`ListWithAgentInfo`, `GetDetailWithAgentInfo`
    - 权限规则：代理只能看到其路径下用户的提现记录

13. **exchange/orders（兑换订单）**
    - 文件：`internal/dao/exchange_orders.go`
    - 方法：`ListWithAgentInfo`, `ListAdminExchangeOrdersWithFullInfo`
    - 权限规则：代理只能看到其路径下用户的兑换订单

## 使用示例

### 单用户权限过滤示例

```go
// ListAdminWalletsWithFullInfo 查询钱包列表
func (d *walletsDao) ListAdminWalletsWithFullInfo(ctx context.Context, page int, pageSize int, condition g.Map) (list []*model.WalletAdminInfo, total int, err error) {
    // 构建查询
    m := d.Ctx(ctx).As("w").
        LeftJoin("users u", "w.user_id = u.id")
    
    // 添加代理权限过滤
    httpReq := g.RequestFromCtx(ctx)
    if httpReq != nil {
        agentId := httpReq.GetCtxVar("agentId").Int64()
        
        if agentId > 0 {
            var currentAgent *entity.Agents
            err := Agents.Ctx(ctx).
                Where(Agents.Columns().AgentId, agentId).
                WhereNull(Agents.Columns().DeletedAt).
                Scan(&currentAgent)
            
            if err != nil || currentAgent == nil {
                return nil, 0, gerror.NewCode(codes.CodeForbidden, "无权访问钱包数据")
            }
            
            m = m.WhereLike("u.agent_relationship", currentAgent.Relationship+"%")
        }
    }
    
    // 执行查询...
}
```

### 双向权限过滤示例

```go
// 转账记录 - 发送方或接收方任一方在权限范围内即可访问
m = m.Where(
    "(sender.agent_relationship LIKE ? OR receiver.agent_relationship LIKE ?)",
    currentAgent.Relationship+"%",
    currentAgent.Relationship+"%",
)
```

## 测试

### 测试数据准备

```sql
-- 创建测试代理
INSERT INTO agents (agent_id, relationship, parent_agent_id, username) VALUES
(1, '/1/', NULL, 'agent1'),
(3, '/1/3/', 1, 'agent3'),
(4, '/1/3/4/', 3, 'agent4');

-- 创建测试用户
INSERT INTO users (id, agent_relationship, account) VALUES
(1, '/1/3/4/', 'user1'),
(2, '/1/3/', 'user2'),
(3, '/1/', 'user3');
```

### 预期结果

- 代理1登录：可以看到所有用户（user1, user2, user3）
- 代理3登录：可以看到user1和user2
- 代理4登录：只能看到user1

## 注意事项

1. **性能优化**
   - 必须在`agent_relationship`和`relationship`字段上创建索引
   - LIKE查询使用前缀匹配（`LIKE 'prefix%'`），可以利用索引
   - 对于大量数据的查询，考虑使用缓存

2. **数据一致性**
   - 确保所有用户都有正确的`agent_relationship`值
   - 新用户创建时必须设置正确的代理关系
   - 代理的`relationship`字段必须正确维护

3. **安全性**
   - 权限检查在DAO层实现，确保所有查询都经过权限过滤
   - 不依赖前端传递的权限参数
   - 每次查询都验证代理的存在性和有效性

4. **系统设计**
   - 这是纯代理系统，没有管理员用户
   - 所有用户都从`agents`表认证
   - 权限过滤对所有代理生效

## 部署建议

1. **数据库索引**
   ```sql
   -- 用户表索引
   CREATE INDEX idx_users_agent_relationship ON users(agent_relationship);
   
   -- 代理表索引
   CREATE INDEX idx_agents_relationship ON agents(relationship);
   CREATE INDEX idx_agents_parent_agent_id ON agents(parent_agent_id);
   ```

2. **监控**
   - 监控慢查询，特别是使用LIKE操作的查询
   - 记录代理用户的访问日志
   - 监控权限拒绝的频率

3. **测试**
   - 部署前进行完整的权限测试
   - 验证代理只能看到授权范围内的数据
   - 测试边界情况（空值、删除的代理等）

## 错误处理

- **列表查询**：权限不足时返回空列表
- **详情查询**：权限不足时返回`codes.CodeForbidden`错误
- **错误信息**：统一使用"无权访问[资源类型]数据"格式

## 未来扩展

1. **缓存优化**：可以缓存代理的`relationship`值，减少数据库查询
2. **批量权限检查**：对于批量操作，可以预先过滤有权限的数据ID
3. **权限日志**：记录详细的权限检查日志，便于审计和问题排查