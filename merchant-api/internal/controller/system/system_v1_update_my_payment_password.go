package system

import (
	"context"

	"merchant-api/internal/service"

	v1 "merchant-api/api/system/v1"
)

// UpdateMyPaymentPassword implements the logic for merchants to update their payment password.
func (c *ControllerV1) UpdateMyPaymentPassword(ctx context.Context, req *v1.UpdateMyPaymentPasswordReq) (res *v1.UpdateMyPaymentPasswordRes, err error) {
	// 直接调用服务层，服务层会从上下文获取商户ID并验证权限
	res, err = service.SystemServiceInstance.UpdateMyPaymentPassword(ctx, req)
	return
}