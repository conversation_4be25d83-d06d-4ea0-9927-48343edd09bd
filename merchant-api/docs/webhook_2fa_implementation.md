# Webhook Secret 2FA Implementation

This document describes the implementation of Google 2FA verification for webhook secret operations.

## Overview

To enhance security, both viewing and resetting webhook secrets now require Google 2FA verification when 2FA is enabled for the merchant account.

## Implementation Details

### 1. API Changes

#### GetMyWebhookSecret API
- Added `code` field to `GetMyWebhookSecretReq`:
  ```go
  Code string `json:"code" v:"length:6,6" dc:"2FA验证码（如果启用）"`
  ```

### 2. Service Layer Changes

#### MerchantWebhookService

- **GetWebhookSecretMasked**: Now requires 2FA verification
  - Added `code` parameter
  - Checks if merchant has 2FA enabled (`Google2FaEnabled == 1`)
  - Verifies the 2FA code using `utility.VerifyGoogle2FACode`
  - Returns error if 2FA is enabled but code is missing or invalid

- **ResetMyWebhookSecret**: Enhanced with 2FA verification
  - Already had `code` parameter in signature
  - Now properly implements 2FA verification
  - Verifies 2FA code before allowing webhook secret reset

### 3. Controller Layer Changes

#### GetMyWebhookSecret Controller
- Passes the 2FA code from request to the service method
- No additional validation needed at controller level

#### ResetMyWebhookSecret Controller
- Added password verification using `AuthService`
- Retrieves username from context for password verification
- Verifies password before calling webhook service

### 4. Security Flow

1. **For Viewing Webhook Secret**:
   - If 2FA is enabled → Require valid 2FA code
   - If 2FA is disabled → No code required
   - Returns masked webhook secret

2. **For Resetting Webhook Secret**:
   - Always require password verification
   - If 2FA is enabled → Also require valid 2FA code
   - Returns new webhook secret in full

### 5. Error Messages

- "需要提供2FA验证码" - When 2FA is enabled but code is not provided
- "2FA验证码错误" - When provided 2FA code is invalid
- "密码错误" - When password verification fails

### 6. Testing

Use the updated test script:
```bash
# View webhook secret without 2FA
curl -X GET "http://localhost:8002/api/v1/my/webhook-secret" \
  -H "X-API-Key: YOUR_API_KEY"

# View webhook secret with 2FA
curl -X GET "http://localhost:8002/api/v1/my/webhook-secret?code=123456" \
  -H "X-API-Key: YOUR_API_KEY"

# Reset webhook secret
curl -X PUT "http://localhost:8002/api/v1/my/webhook-secret" \
  -H "X-API-Key: YOUR_API_KEY" \
  -H "Content-Type: application/json" \
  -d '{
    "password": "your_password",
    "code": "123456"
  }'
```

## Security Considerations

1. **2FA Enforcement**: The system automatically enforces 2FA verification when it's enabled for the merchant
2. **Password Protection**: Resetting webhook secret always requires password verification
3. **Masked Display**: Viewing webhook secret returns a masked version (first 12 and last 8 characters visible)
4. **Error Handling**: Generic error messages prevent information leakage about 2FA status

## Migration Notes

- Existing API clients need to be updated to provide 2FA code when viewing webhook secrets if 2FA is enabled
- The GET endpoint now accepts a query parameter `code` for 2FA verification