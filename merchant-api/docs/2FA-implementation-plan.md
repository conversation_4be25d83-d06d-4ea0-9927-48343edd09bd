# 商户2FA双因子认证功能实现计划

## 📋 项目概述

**项目名称**: 商户平台Google 2FA双因子认证功能  
**技术栈**: GoFrame v2 + Casdoor单点登录 + Google Authenticator  
**目标用户**: 商户 (merchants表)  
**实现方式**: 在Casdoor认证成功后增加2FA验证层

## 🎯 核心需求

1. **未启用2FA（Google2FaEnabled=0）**: 
   - Casdoor认证成功后，展示2FA设置页面
   - 生成并显示TOTP密钥和二维码
   - 用户通过Google Authenticator扫码后验证
   - 验证成功后设置Google2FaEnabled=1，生成恢复码

2. **已启用2FA（Google2FaEnabled=1）**: 
   - Casdoor认证成功后，要求输入2FA验证码
   - 验证通过后才发放最终的访问token
   - 利用LoginAttempts和LockedUntil字段防止暴力破解

3. **集成要点**:
   - 保持现有Casdoor认证流程不变
   - 在认证成功后增加2FA验证步骤
   - 结合数据库字段和Redis缓存实现安全控制

## 🏗️ 系统架构设计

### 技术架构流程

```mermaid
flowchart LR
    subgraph "前端"
        A[登录页面]
        B[2FA设置页面]
        C[2FA验证页面]
    end
    
    subgraph "后端API"
        D[/auth/casdoor/signin]
        E[/auth/2fa/verify]
        F[/auth/2fa/setup]
    end
    
    subgraph "业务逻辑"
        G[CasdoorSignin方法]
        H[Verify2FA方法]
        I[Setup2FA方法]
    end
    
    subgraph "数据存储"
        J[(Merchants表)]
        K[(Redis缓存)]
    end
    
    subgraph "外部服务"
        L[Casdoor SSO]
        M[Google Auth]
    end
    
    A --> D --> G --> L
    G --> J
    G --> K
    G -.->|IsFirst=1| B
    G -.->|IsFirst=0| C
    B --> F --> I
    C --> E --> H
    I --> J
    H --> K
```

## 📊 数据结构设计

### Merchants表字段（新表结构）

| 字段名 | 数据库列名 | 类型 | 说明 | 默认值 |
|--------|------------|------|------|--------|
| `Google2FaSecret` | `google2fa_secret` | string | Google Authenticator密钥 | NULL |
| `Google2FaEnabled` | `google2fa_enabled` | int | 是否启用2FA (0=未启用, 1=已启用) | 0 |
| `RecoveryCodes` | `recovery_codes` | string | 恢复码JSON数组 | NULL |
| `LoginAttempts` | `login_attempts` | int | 登录尝试次数 | 0 |
| `LockedUntil` | `locked_until` | datetime | 账户锁定到期时间 | NULL |
| `Email` | `email` | string | 商户邮箱 (用于登录和通知) | - |

### Redis缓存结构

```go
// 2FA设置临时状态
Key: "2fa:setup:{merchantId}"
Value: {
  "secret": "TOTP密钥",
  "casdoorToken": "原始token",
  "username": "<EMAIL>"
}
TTL: 900秒 (15分钟)

// 2FA验证临时状态  
Key: "2fa:verify:{merchantId}"
Value: {
  "casdoorToken": "原始token",
  "username": "<EMAIL>",
  "attempts": 0  // 尝试次数
}
TTL: 900秒 (15分钟)

// 防暴力破解计数器
Key: "2fa:attempts:{merchantId}"
Value: 失败次数
TTL: 1800秒 (30分钟)
```

## 🔌 API接口设计

### 1. 修改现有登录响应

**文件**: `api/system/v1/auth.go`

```go
// 扩展CasdoorSigninRes结构
type CasdoorSigninRes struct {
    oauth2.Token `json:"accessToken"`
    
    // 2FA相关字段
    RequireSetup2FA bool   `json:"requireSetup2FA"` // 需要设置2FA
    Require2FA      bool   `json:"require2FA"`      // 需要验证2FA
    TotpSecret      string `json:"totpSecret,omitempty"` // TOTP密钥(仅首次)
    QRCodeURL       string `json:"qrCodeURL,omitempty"`  // 二维码URL
    MerchantId      uint   `json:"merchantId"`           // 商户ID
}
```

### 2. 新增2FA接口

```go
// 2FA验证接口
type Verify2FAReq struct {
    g.Meta     `path:"/auth/2fa/verify" method:"post" tags:"SystemAuth" summary:"验证2FA"`
    Code       string `json:"code" v:"required|length:6,6#验证码不能为空|验证码必须为6位"`
    MerchantId uint   `json:"merchantId" v:"required#商户ID不能为空"`
}

type Verify2FARes struct {
    Token    string `json:"token"`    // 最终认证token
    ExpireAt int64  `json:"expireAt"` // 过期时间
}

// 2FA设置接口
type Setup2FAReq struct {
    g.Meta     `path:"/auth/2fa/setup" method:"post" tags:"SystemAuth" summary:"设置2FA"`
    Code       string `json:"code" v:"required|length:6,6#验证码不能为空|验证码必须为6位"`
    MerchantId uint   `json:"merchantId" v:"required#商户ID不能为空"`
}

type Setup2FARes struct {
    Token         string   `json:"token"`         // 最终认证token
    ExpireAt      int64    `json:"expireAt"`      // 过期时间
    RecoveryCodes []string `json:"recoveryCodes"` // 恢复码
}
```

### 3. 服务接口定义

**文件**: `internal/service/system.go`

```go
type ISystemService interface {
    // 现有方法...
    
    // 2FA相关方法
    Verify2FA(ctx context.Context, req *v1.Verify2FAReq) (*v1.Verify2FARes, error)
    Setup2FA(ctx context.Context, req *v1.Setup2FAReq) (*v1.Setup2FARes, error)
    
    // 辅助方法
    GenerateTOTPSecret(ctx context.Context, merchantId uint) (secret, qrURL string, err error)
    VerifyTOTPCode(ctx context.Context, merchantId uint, code string) (bool, error)
}
```

## 🔄 业务流程设计

### 登录流程图

```mermaid
flowchart TD
    A[用户点击登录] --> B[跳转Casdoor登录]
    B --> C[Casdoor验证成功]
    C --> D[调用/auth/casdoor/signin]
    D --> E[查询商户信息<br/>通过email字段]
    E --> F{检查google2fa_enabled}
    
    F -->|google2fa_enabled=0| G[未启用2FA流程]
    F -->|google2fa_enabled=1| H[已启用2FA流程]
    
    G --> I[生成TOTP密钥]
    I --> J[生成二维码URL]
    J --> K[缓存设置信息到Redis]
    K --> L[返回 requireSetup2FA=true]
    L --> M[前端展示2FA设置页]
    M --> N[用户扫码+输入验证码]
    N --> O[调用/auth/2fa/setup]
    O --> P{验证成功?}
    
    H --> Q{检查账户锁定}
    Q -->|未锁定| R[缓存验证信息到Redis]
    Q -->|已锁定| Z1[返回账户锁定错误]
    R --> S[返回 require2FA=true]
    S --> T[前端展示2FA验证页]
    T --> U[用户输入验证码]
    U --> V[调用/auth/2fa/verify]
    V --> W{验证成功?}
    
    P -->|是| X[更新google2fa_enabled=1<br/>保存google2fa_secret]
    P -->|否| Y[返回错误]
    W -->|是| Z[清空login_attempts<br/>生成最终Token]
    W -->|否| AA[增加login_attempts<br/>检查是否需要锁定]
    
    X --> AB[生成恢复码]
    AB --> AC[生成最终Token]
    Y --> N
    AA --> T
    Z --> AD[登录成功]
    AC --> AD
    Z1 --> AE[登录失败]
```

### 前后端交互序列图

```mermaid
sequenceDiagram
    participant U as 用户
    participant F as 前端
    participant B as 后端
    participant R as Redis
    participant C as Casdoor
    participant G as Google Auth
    
    U->>F: 点击登录
    F->>C: 跳转Casdoor登录
    C->>F: 返回code和state
    F->>B: POST /auth/casdoor/signin
    B->>C: 验证token
    B->>B: 查询商户信息
    
    alt 未启用2FA (google2fa_enabled=0)
        B->>B: 生成TOTP密钥
        B->>R: 缓存设置信息
        B->>F: requireSetup2FA=true + 二维码
        F->>U: 展示2FA设置页面
        U->>G: 扫描二维码
        G->>U: 显示6位验证码
        U->>F: 输入验证码
        F->>B: POST /auth/2fa/setup
        B->>R: 获取缓存的密钥
        B->>B: 验证TOTP码
        alt 验证成功
            B->>B: 更新google2fa_enabled=1
            B->>B: 保存google2fa_secret
            B->>R: 清除缓存
            B->>F: token + 恢复码
            F->>U: 显示恢复码
        else 验证失败
            B->>F: 错误信息
            F->>U: 提示重试
        end
    else 已启用2FA (google2fa_enabled=1)
        B->>B: 检查locked_until
        alt 账户未锁定
            B->>R: 缓存验证信息
            B->>F: require2FA=true + merchantId
            F->>U: 展示2FA验证页面
            U->>G: 查看验证码
            U->>F: 输入验证码
            F->>B: POST /auth/2fa/verify
            B->>B: 验证TOTP码
            alt 验证成功
                B->>B: 清空login_attempts
                B->>R: 清除缓存
                B->>F: 最终token
            else 验证失败
                B->>B: 增加login_attempts
                B->>B: 检查是否超过5次
                alt 超过限制
                    B->>B: 设置locked_until
                    B->>F: 账户锁定错误
                else 未超限制
                    B->>F: 验证失败错误
                end
                F->>U: 提示重试
            end
        else 账户已锁定
            B->>F: 返回锁定错误
            F->>U: 显示锁定提示
        end
    end
    
    F->>U: 登录成功，跳转主页
```

## 💻 核心代码实现

### 1. 修改CasdoorSignin方法

**文件**: `internal/logic/system/v1/auth.go`

```go
// CasdoorSignin 修改已有方法，增加2FA检查
func (l *sSystemLogic) CasdoorSignin(ctx context.Context, req *v1.CasdoorSigninReq) (res *v1.CasdoorSigninRes, err error) {
    // 现有Casdoor认证逻辑保持不变...
    code := req.Code
    state := req.State
    
    token, err := casdoorsdk.GetOAuthToken(code, state)
    if err != nil {
        // 现有错误处理...
    }
    
    claims, err := casdoorsdk.ParseJwtToken(token.AccessToken)
    if err != nil {
        return nil, codes.WrapError(err, codes.CodeInternalError)
    }
    
    // 现有缓存和权限设置逻辑...
    
    // 新增：查询商户信息以检查2FA状态
    var merchantInfo *entity.Merchants
    err = dao.Merchants.Ctx(ctx).
        Where(dao.Merchants.Columns().Email, claims.Name). // Casdoor用户名对应商户email
        WhereNull(dao.Merchants.Columns().DeletedAt).
        Scan(&merchantInfo)
    
    if err != nil {
        return nil, codes.WrapError(err, codes.CodeInternalError)
    }
    if merchantInfo == nil {
        return nil, codes.NewError(codes.CodeMerchantNotFound)
    }
    
    // 构建基本响应
    res = &v1.CasdoorSigninRes{
        Token:      *token,
        MerchantId: merchantInfo.MerchantId,
    }
    
    // 检查2FA状态和账户锁定状态
    if merchantInfo.LockedUntil != nil && merchantInfo.LockedUntil.After(gtime.Now()) {
        // 账户已锁定
        remainMinutes := merchantInfo.LockedUntil.Sub(gtime.Now()).Minutes()
        return nil, codes.NewErrorf(codes.Code2FALocked, 
            "账户已锁定，请%.0f分钟后重试", remainMinutes)
    }
    
    if merchantInfo.Google2FaEnabled == 0 {
        // 未启用2FA - 需要设置
        secret, qrURL, err := l.GenerateTOTPSecret(ctx, merchantInfo.MerchantId)
        if err != nil {
            return nil, err
        }
        
        // 缓存2FA设置信息
        cacheKey := fmt.Sprintf("2fa:setup:%d", merchantInfo.MerchantId)
        cacheData := g.Map{
            "secret":       secret,
            "casdoorToken": token.AccessToken,
            "username":     claims.Name,
        }
        cache.GetInstance().Set(ctx, cacheKey, cacheData, 900) // 15分钟
        
        res.RequireSetup2FA = true
        res.TotpSecret = secret
        res.QRCodeURL = qrURL
    } else {
        // 已启用2FA - 需要验证
        // 缓存验证信息
        cacheKey := fmt.Sprintf("2fa:verify:%d", merchantInfo.MerchantId)
        cacheData := g.Map{
            "casdoorToken": token.AccessToken,
            "username":     claims.Name,
        }
        cache.GetInstance().Set(ctx, cacheKey, cacheData, 900) // 15分钟
        
        res.Require2FA = true
    }
    
    return res, nil
}

// GenerateTOTPSecret 生成TOTP密钥和二维码
func (l *sSystemLogic) GenerateTOTPSecret(ctx context.Context, merchantId uint) (secret, qrURL string, err error) {
    // 使用pquerna/otp库生成密钥
    key, err := totp.Generate(totp.GenerateOpts{
        Issuer:      "XPay商户平台",
        AccountName: fmt.Sprintf("merchant_%d", merchantId),
    })
    if err != nil {
        return "", "", codes.WrapError(err, codes.CodeInternalError)
    }
    
    secret = key.Secret()
    qrURL = key.URL()
    return secret, qrURL, nil
}

// VerifyTOTPCode 验证TOTP代码
func (l *sSystemLogic) VerifyTOTPCode(ctx context.Context, merchantId uint, code string) (bool, error) {
    // 获取商户信息
    var merchant *entity.Merchants
    err := dao.Merchants.Ctx(ctx).
        Where(dao.Merchants.Columns().MerchantId, merchantId).
        WhereNull(dao.Merchants.Columns().DeletedAt).
        Scan(&merchant)
    
    if err != nil || merchant == nil {
        return false, codes.NewError(codes.CodeMerchantNotFound)
    }
    
    // 验证TOTP码，使用Google2FaSecret字段
    valid := totp.Validate(code, merchant.Google2FaSecret)
    return valid, nil
}
```

### 2. 实现2FA验证接口

**文件**: `internal/logic/system/v1/auth.go` (继续添加)

```go
// Verify2FA 验证2FA代码
func (l *sSystemLogic) Verify2FA(ctx context.Context, req *v1.Verify2FAReq) (*v1.Verify2FARes, error) {
    // 获取商户信息，检查账户锁定状态
    var merchant *entity.Merchants
    err := dao.Merchants.Ctx(ctx).
        Where(dao.Merchants.Columns().MerchantId, req.MerchantId).
        WhereNull(dao.Merchants.Columns().DeletedAt).
        Scan(&merchant)
    
    if err != nil || merchant == nil {
        return nil, codes.NewError(codes.CodeMerchantNotFound)
    }
    
    // 检查账户是否被锁定
    if merchant.LockedUntil != nil && merchant.LockedUntil.After(gtime.Now()) {
        remainMinutes := merchant.LockedUntil.Sub(gtime.Now()).Minutes()
        return nil, codes.NewErrorf(codes.Code2FALocked, 
            "账户已锁定，请%.0f分钟后重试", remainMinutes)
    }
    
    // 获取缓存的验证信息
    cacheKey := fmt.Sprintf("2fa:verify:%d", req.MerchantId)
    cacheData, err := cache.GetInstance().Get(ctx, cacheKey)
    if err != nil || cacheData.IsEmpty() {
        return nil, codes.NewError(codes.Code2FATempTokenInvalid)
    }
    
    // 验证2FA代码
    valid, err := l.VerifyTOTPCode(ctx, req.MerchantId, req.Code)
    if err != nil {
        return nil, err
    }
    
    if !valid {
        // 增加登录尝试次数
        newAttempts := merchant.LoginAttempts + 1
        updateData := g.Map{
            dao.Merchants.Columns().LoginAttempts: newAttempts,
        }
        
        // 如果超过5次，锁定账户
        if newAttempts >= 5 {
            updateData[dao.Merchants.Columns().LockedUntil] = gtime.Now().Add(30 * time.Minute)
        }
        
        _, err = dao.Merchants.Ctx(ctx).
            Data(updateData).
            Where(dao.Merchants.Columns().MerchantId, req.MerchantId).
            Update()
        
        if newAttempts >= 5 {
            return nil, codes.NewError(codes.Code2FALocked)
        }
        return nil, codes.NewError(codes.Code2FAInvalid)
    }
    
    // 验证成功，清空登录尝试次数
    _, err = dao.Merchants.Ctx(ctx).
        Data(g.Map{
            dao.Merchants.Columns().LoginAttempts: 0,
            dao.Merchants.Columns().LastLoginTime: gtime.Now(),
            dao.Merchants.Columns().LastLoginIp:   g.RequestFromCtx(ctx).GetClientIp(),
        }).
        Where(dao.Merchants.Columns().MerchantId, req.MerchantId).
        Update()
    
    // 生成最终token
    cacheMap := cacheData.Map()
    token := cacheMap["casdoorToken"].(string)
    expireAt := time.Now().Add(24 * time.Hour).Unix()
    
    // 清除缓存
    cache.GetInstance().Remove(ctx, cacheKey)
    
    return &v1.Verify2FARes{
        Token:    token,
        ExpireAt: expireAt,
    }, nil
}

// Setup2FA 设置2FA
func (l *sSystemLogic) Setup2FA(ctx context.Context, req *v1.Setup2FAReq) (*v1.Setup2FARes, error) {
    // 获取缓存的设置信息
    cacheKey := fmt.Sprintf("2fa:setup:%d", req.MerchantId)
    cacheData, err := cache.GetInstance().Get(ctx, cacheKey)
    if err != nil || cacheData.IsEmpty() {
        return nil, codes.NewError(codes.Code2FATempTokenInvalid)
    }
    
    cacheMap := cacheData.Map()
    secret := cacheMap["secret"].(string)
    
    // 验证2FA代码
    valid := totp.Validate(req.Code, secret)
    if !valid {
        return nil, codes.NewError(codes.Code2FAInvalid)
    }
    
    // 生成恢复码
    recoveryCodes := l.generateRecoveryCodes()
    recoveryCodesJSON, _ := json.Marshal(recoveryCodes)
    
    // 更新数据库
    _, err = dao.Merchants.Ctx(ctx).
        Data(g.Map{
            dao.Merchants.Columns().Google2FaSecret:  secret,
            dao.Merchants.Columns().Google2FaEnabled: 1,
            dao.Merchants.Columns().RecoveryCodes:    string(recoveryCodesJSON),
        }).
        Where(dao.Merchants.Columns().MerchantId, req.MerchantId).
        Update()
    
    if err != nil {
        return nil, codes.WrapError(err, codes.CodeInternalError)
    }
    
    // 生成最终token
    token := cacheMap["casdoorToken"].(string)
    expireAt := time.Now().Add(24 * time.Hour).Unix()
    
    // 清除缓存
    cache.GetInstance().Remove(ctx, cacheKey)
    
    return &v1.Setup2FARes{
        Token:         token,
        ExpireAt:      expireAt,
        RecoveryCodes: recoveryCodes,
    }, nil
}

// generateRecoveryCodes 生成恢复码
func (l *sSystemLogic) generateRecoveryCodes() []string {
    codes := make([]string, 10)
    for i := 0; i < 10; i++ {
        codes[i] = grand.S(8, true) // 使用GoFrame的随机字符串生成
    }
    return codes
}
```

### 3. 注册路由和中间件配置

**文件**: `internal/cmd/http/main.go` (或相应的路由注册文件)

```go
// 在路由注册中添加
group := s.Group("/api")
{
    // 现有的认证路由
    group.POST("/auth/casdoor/signin", controller.System.CasdoorSignin)
    
    // 新增2FA路由（不需要认证中间件）
    group.POST("/auth/2fa/verify", controller.System.Verify2FA)
    group.POST("/auth/2fa/setup", controller.System.Setup2FA)
}
```

**文件**: `internal/middleware/auth.go`

```go
// 添加到免认证路由列表
var noAuthRoutes = []string{
    "/api/system/captcha",
    "/api/system/auth/casdoor/signin",
    "/api/auth/2fa/verify",    // 新增
    "/api/auth/2fa/setup",     // 新增
    "/api/auth/login",
}
```

## 🔒 安全策略

### 1. 错误码定义

**文件**: `internal/codes/codes.go` (添加到现有错误码中)

```go
// 在业务错误码 - 认证模块 (1000-1999) 中添加
Code2FARequired         = gcode.New(1012, "需要进行2FA验证", nil)
Code2FAInvalid          = gcode.New(1013, "2FA验证码无效", nil)
Code2FASetupRequired    = gcode.New(1014, "需要设置2FA双因子认证", nil)
Code2FATempTokenInvalid = gcode.New(1015, "临时认证信息无效或已过期", nil)
Code2FALocked           = gcode.New(1016, "2FA验证失败次数过多，请稍后重试", nil)
```

### 2. 安全措施实现

| 安全策略 | 实现方式 | 说明 |
|----------|------------|------|
| **防暴力破解** | 数据库字段 | 使用LoginAttempts和LockedUntil字段，5次失败后锁定30分钟 |
| **会话安全** | Redis缓存 | 15分钟有效期 |
| **时间容差** | TOTP标准 | 使用pquerna/otp库默认配置 |
| **恢复码** | 加密存储 | 10个8位随机字符串 |
| **审计日志** | 数据库记录 | 记录LastLoginTime和LastLoginIp |

### 3. 防暴力破解实现

```go
// 在Verify2FA方法中使用数据库字段实现
// 检查账户是否被锁定
if merchant.LockedUntil != nil && merchant.LockedUntil.After(gtime.Now()) {
    remainMinutes := merchant.LockedUntil.Sub(gtime.Now()).Minutes()
    return nil, codes.NewErrorf(codes.Code2FALocked, 
        "账户已锁定，请%.0f分钟后重试", remainMinutes)
}

// 验证失败后更新数据库
newAttempts := merchant.LoginAttempts + 1
updateData := g.Map{
    dao.Merchants.Columns().LoginAttempts: newAttempts,
}

// 如果超过5次，锁定账户
if newAttempts >= 5 {
    updateData[dao.Merchants.Columns().LockedUntil] = gtime.Now().Add(30 * time.Minute)
}

// 验证成功后清空尝试次数
updateData[dao.Merchants.Columns().LoginAttempts] = 0
updateData[dao.Merchants.Columns().LastLoginTime] = gtime.Now()
updateData[dao.Merchants.Columns().LastLoginIp] = g.RequestFromCtx(ctx).GetClientIp()
```

## 🚀 部署和配置

### 1. 环境配置

**文件**: `manifest/config/config.yaml`

```yaml
# 2FA相关配置
twofa:
  enabled: true                         # 是否启用2FA
  issuer: "XPay商户平台"             # Google Authenticator中显示的名称
  cache_expire: 900                     # 缓存过期时间(15分钟)
  max_attempts: 5                       # 最大尝试次数
  lockout_duration: 1800                # 锁定时间(30分钟)
  recovery_codes_count: 10              # 恢复码数量
```

### 2. 依赖包管理

```bash
# 安装所需依赖
go get github.com/pquerna/otp  # 已存在于项目中
```

## 📱 前端集成指南

### 1. 登录流程处理

```javascript
// 处理Casdoor登录响应
const handleCasdoorSignin = async (response) => {
  if (response.requireSetup2FA) {
    // 首次登录 - 显示2FA设置
    showSetup2FADialog({
      qrCodeURL: response.qrCodeURL,
      totpSecret: response.totpSecret,
      merchantId: response.merchantId
    })
  } else if (response.require2FA) {
    // 常规登录 - 显示2FA验证
    showVerify2FADialog({
      merchantId: response.merchantId
    })
  } else {
    // 登录成功
    handleLoginSuccess(response.accessToken)
  }
}

// 2FA设置处理
const handleSetup2FA = async (code, merchantId) => {
  try {
    const res = await api.post('/auth/2fa/setup', {
      code,
      merchantId
    })
    
    // 显示恢复码
    showRecoveryCodes(res.recoveryCodes)
    
    // 登录成功
    handleLoginSuccess(res.token)
  } catch (error) {
    showError('验证码错误，请重试')
  }
}

// 2FA验证处理
const handleVerify2FA = async (code, merchantId) => {
  try {
    const res = await api.post('/auth/2fa/verify', {
      code,
      merchantId
    })
    
    handleLoginSuccess(res.token)
  } catch (error) {
    if (error.code === 1016) { // Code2FALocked
      showError('尝试次数过多，请稍后重试')
    } else {
      showError('验证码错误')
    }
  }
}
```

### 2. UI组件建议

- **2FA设置弹窗**: 显示二维码、密钥、验证码输入框
- **2FA验证弹窗**: 简洁的6位验证码输入框
- **恢复码显示**: 强制用户保存后才能继续
- **错误提示**: 明确显示剩余尝试次数

## 📋 实施计划

### 重要说明

📢 **数据库无需修改**：商户表已经包含所有必需字段：
- `google2fa_secret` - 存储TOTP密钥
- `google2fa_enabled` - 控制是否启用2FA
- `recovery_codes` - 存储恢复码
- `login_attempts` - 记录失败次数
- `locked_until` - 账户锁定时间

### 第一阶段：后端核心开发（1-2天）

1. **修改现有代码**
   - [ ] 修改 `CasdoorSignin` 方法，增加2FA检查逻辑
   - [ ] 添加 `GenerateTOTPSecret` 和 `VerifyTOTPCode` 方法
   - [ ] 更新API响应结构

2. **实现2FA接口**
   - [ ] 实现 `Verify2FA` 接口
   - [ ] 实现 `Setup2FA` 接口
   - [ ] 添加路由注册

3. **错误处理**
   - [ ] 添加2FA相关错误码
   - [ ] 实现防暴力破解机制

### 第二阶段：前端集成（1-2天）

1. **UI组件开发**
   - [ ] 2FA设置弹窗（二维码、密钥、验证）
   - [ ] 2FA验证弹窗
   - [ ] 恢复码显示和保存组件

2. **交互逻辑**
   - [ ] 修改登录流程处理
   - [ ] 集成API调用
   - [ ] 错误处理和重试机制

### 第三阶段：测试与优化（1天）

1. **功能测试**
   - [ ] 首次登录设置流程
   - [ ] 常规登录验证流程
   - [ ] 错误场景测试

2. **安全测试**
   - [ ] 防暴力破解测试
   - [ ] 会话超时测试
   - [ ] 恢复码使用测试

## 🧪 测试策略

### 1. 单元测试

```go
// 测试TOTP生成和验证
func TestGenerateTOTPSecret(t *testing.T) {
    ctx := context.Background()
    logic := &sSystemLogic{}
    
    secret, qrURL, err := logic.GenerateTOTPSecret(ctx, 123)
    t.Assert(err, nil)
    t.Assert(secret != "", true)
    t.Assert(strings.Contains(qrURL, "otpauth://totp"), true)
}

// 测试防暴力破解
func TestBruteForceProtection(t *testing.T) {
    // 测试5次失败后锁定
    for i := 0; i < 6; i++ {
        _, err := logic.Verify2FA(ctx, &v1.Verify2FAReq{
            Code: "000000",
            MerchantId: 123,
        })
        
        if i < 5 {
            t.Assert(err.(*gerror.Error).Code(), codes.Code2FAInvalid)
        } else {
            t.Assert(err.(*gerror.Error).Code(), codes.Code2FALocked)
        }
    }
}
```

### 2. 集成测试

- **场景1**: 新商户首次登录设置2FA
- **场景2**: 老商户登录输入2FA验证码
- **场景3**: 验证码错误重试
- **场景4**: 会话超时处理

### 3. 性能测试

- Redis缓存读写性能
- TOTP验证响应时间
- 并发登录压力测试

## 📖 常见问题

### 1. 开发过程中的注意事项

- **Casdoor用户名映射**: Casdoor的username对应商户表的email字段
- **Token处理**: 需要保存原始Casdoor token，2FA验证后再发放
- **缓存管理**: 使用项目现有的cache.GetInstance()
- **错误处理**: 遵循现有的codes错误码体系

### 新表结构优势

根据新的商户表结构，我们的实现方案有以下改进：

1. **更清晰的字段语义**
   - 使用 `Google2FaEnabled` 替代 `IsFirst`，更明确表达2FA启用状态
   - 使用 `Google2FaSecret` 统一存储Google Authenticator密钥

2. **更安全的防暴力破解**
   - 利用数据库 `LoginAttempts` 和 `LockedUntil` 字段
   - 持久化存储，重启不丢失锁定状态
   - 精确计算剩余锁定时间

3. **更完善的审计跟踪**
   - 自动记录 `LastLoginTime` 和 `LastLoginIp`
   - 方便商户查看登录历史

### 2. 用户使用指南

**首次设置2FA**:
1. 使用Casdoor账号登录
2. 系统自动显示2FA设置页面
3. 使用Google Authenticator扫描二维码
4. 输入APP显示的6位验证码
5. 保存显示的恢复码

**常规登录**:
1. 使用Casdoor账号登录
2. 输入Google Authenticator显示的验证码
3. 完成登录

### 3. 故障排除

- **验证码无效**: 检查手机时间是否与服务器同步
- **账户被锁定**: 等待30分钟后重试
- **丢失手机**: 使用恢复码登录（功能待实现）

---

**文档版本**: v3.0  
**创建时间**: 2024-06-25  
**更新时间**: 2024-06-25  
**作者**: 开发团队

> 注：本文档已根据最新的商户表结构进行更新，使用Google2FaEnabled、Google2FaSecret、LoginAttempts和LockedUntil等字段实现2FA功能和安全控制。