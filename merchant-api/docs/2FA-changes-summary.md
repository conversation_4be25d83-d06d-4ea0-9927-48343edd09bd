# 2FA实现计划更新总结

## 根据新的商户表结构进行的主要更改

### 1. 字段映射变化

| 原字段 | 新字段 | 说明 |
|--------|--------|------|
| `IsFirst` (string) | `Google2FaEnabled` (int) | 0=未启用2FA，1=已启用2FA |
| `TotpSecret` | `Google2FaSecret` | 统一使用Google2FA前缀 |
| - | `LoginAttempts` (int) | 新增：记录失败尝试次数 |
| - | `LockedUntil` (datetime) | 新增：账户锁定时间 |

### 2. 业务逻辑调整

#### 判断是否需要设置2FA
```go
// 旧逻辑
if merchantInfo.IsFirst == "1"

// 新逻辑
if merchantInfo.Google2FaEnabled == 0
```

#### 防暴力破解机制
```go
// 旧逻辑：仅使用Redis
attemptsKey := fmt.Sprintf("2fa:attempts:%d", req.MerchantId)
cache.GetInstance().Incr(ctx, attemptsKey)

// 新逻辑：使用数据库字段
newAttempts := merchant.LoginAttempts + 1
updateData := g.Map{
    dao.Merchants.Columns().LoginAttempts: newAttempts,
}
if newAttempts >= 5 {
    updateData[dao.Merchants.Columns().LockedUntil] = gtime.Now().Add(30 * time.Minute)
}
```

### 3. 安全性提升

1. **持久化锁定状态**：使用数据库字段存储，重启不丢失
2. **精确的锁定时间**：可以精确计算剩余锁定时间
3. **审计跟踪**：自动记录LastLoginTime和LastLoginIp

### 4. 实施优势

- **无需数据库迁移**：所有必需字段已存在
- **更清晰的语义**：Google2FaEnabled比IsFirst更明确
- **更完善的安全控制**：结合数据库和Redis缓存

## 执行计划

1. 后端开发只需修改业务逻辑，无需数据库变更
2. 前端无需修改，API接口保持一致
3. 测试重点放在锁定机制和状态转换上