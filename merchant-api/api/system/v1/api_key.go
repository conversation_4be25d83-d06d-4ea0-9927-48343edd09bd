package v1

import (
	"merchant-api/api/common"

	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
)

// ApiKeyInfoType defines the structure for API key information returned in lists or details.
type ApiKeyInfoType struct {
	// ApiKeyId is the unique identifier for the API key.
	ApiKeyId uint `json:"apiKeyId" dc:"API密钥ID"`
	// MerchantId is the ID of the merchant associated with the key.
	MerchantId uint `json:"merchantId" dc:"商户ID"`
	// MerchantName is the name of the merchant.
	MerchantName string `json:"merchantName" dc:"商户名称"`
	// ApiKey is the actual API key string (masked or partial in lists).
	ApiKey string `json:"apiKey" dc:"API密钥"`
	// Label is a user-defined label for the key.
	Label string `json:"label" dc:"标签/名称"`
	// Status indicates the current status of the key (e.g., active, revoked, expired).
	Status string `json:"status" dc:"状态"`
	// Ip<PERSON><PERSON><PERSON><PERSON> specifies the allowed IP addresses (comma-separated).
	IpWhitelist string `json:"ipWhitelist" dc:"IP白名单"`
	// ExpiresAt is the expiration time of the key (nil for never expires).
	ExpiresAt *gtime.Time `json:"expiresAt" dc:"过期时间"`
	// LastUsedAt is the timestamp when the key was last used.
	LastUsedAt *gtime.Time `json:"lastUsedAt" dc:"最后使用时间"`
	// CreatedAt is the timestamp when the key was created.
	CreatedAt *gtime.Time `json:"createdAt" dc:"创建时间"`
}

// ============= 商户自管理API密钥接口 =============
// 以下接口专供商户管理自己的API密钥，无需在URL中指定merchantId

// --- 商户创建自己的API密钥 ---

// CreateMyApiKeyReq defines the request structure for a merchant to create their own API key.
type CreateMyApiKeyReq struct {
	g.Meta      `path:"/my/apikeys" method:"post" tags:"MerchantApiKey" summary:"创建我的API密钥"`
	Label       string      `json:"label" v:"length:0,100" dc:"标签/名称"`
	IpWhitelist string      `json:"ipWhitelist" dc:"IP白名单 (逗号分隔)"`
	ExpiresAt   *gtime.Time `json:"expiresAt" dc:"过期时间 (为空表示永不过期)"`
}

// CreateMyApiKeyRes defines the response structure after a merchant creates their own API key.
type CreateMyApiKeyRes struct {
	// ApiKeyId is the ID of the newly generated API key.
	ApiKeyId uint `json:"apiKeyId" dc:"API密钥ID"`
	// ApiKey is the generated API key.
	ApiKey string `json:"apiKey" dc:"API密钥"`
	// SecretKey is the generated secret key (returned only once upon creation).
	SecretKey string `json:"secretKey" dc:"密钥 (仅在创建时返回一次)"`
}

// --- 商户获取自己的API密钥列表 ---

// GetMyApiKeyListReq defines the request structure for a merchant to get their own API key list.
type GetMyApiKeyListReq struct {
	g.Meta `path:"/my/apikeys" method:"get" tags:"MerchantApiKey" summary:"获取我的API密钥列表"`
	common.PageRequest
	ApiKey     string   `json:"apiKey" dc:"API密钥 (模糊搜索)"`
	Label      string   `json:"label" dc:"标签/名称 (模糊搜索)"`
	Status     string   `json:"status" dc:"状态 (active-可用, revoked-已撤销, expired-已过期)"`
	DateRange  string   `json:"dateRange" dc:"创建时间范围，格式：2025-01-01,2025-01-31"`
	CreatedAt  []string `json:"createdAt" dc:"创建时间范围数组，格式：[开始时间, 结束时间]"`
	Export     bool     `json:"export" dc:"是否导出 (true-导出, false-不导出)"`
}

// GetMyApiKeyListRes defines the response structure for getting a merchant's own API key list.
type GetMyApiKeyListRes struct {
	Page common.PageResponse `json:"page" dc:"分页信息"`
	// Data contains the list of API keys.
	Data []*ApiKeyInfoType `json:"data" dc:"API密钥列表数据"`
}

// --- 商户更新自己的API密钥 ---

// UpdateMyApiKeyReq defines the request structure for a merchant to update their own API key.
type UpdateMyApiKeyReq struct {
	g.Meta      `path:"/my/apikeys/{apiKeyId}" method:"put" tags:"MerchantApiKey" summary:"更新我的API密钥"`
	ApiKeyId    uint        `v:"required#API密钥ID不能为空" dc:"API密钥ID"`
	Label       string      `json:"label" v:"length:0,100" dc:"标签/名称"`
	IpWhitelist string      `json:"ipWhitelist" dc:"IP白名单 (逗号分隔)"`
	ExpiresAt   *gtime.Time `json:"expiresAt" dc:"过期时间 (为空表示永不过期)"`
}

// UpdateMyApiKeyRes defines the response structure for updating a merchant's own API key.
type UpdateMyApiKeyRes struct {
	// Success indicates whether the operation was successful.
	Success bool `json:"success" dc:"是否成功"`
}

// --- 商户删除自己的API密钥 ---

// DeleteMyApiKeyReq defines the request structure for a merchant to delete their own API key.
type DeleteMyApiKeyReq struct {
	g.Meta   `path:"/my/apikeys/{apiKeyId}/delete" method:"post" tags:"MerchantApiKey" summary:"删除我的API密钥"`
	ApiKeyId uint   `v:"required#API密钥ID不能为空" dc:"API密钥ID"`
	Code     string `json:"code" v:"required|length:6,6#验证码不能为空|验证码必须为6位" dc:"2FA验证码"`
}

// DeleteMyApiKeyRes defines the response structure after a merchant deletes their own API key.
type DeleteMyApiKeyRes struct {
	// Success indicates whether the operation was successful.
	Success bool `json:"success" dc:"是否成功"`
}

// --- 商户更新API密钥IP白名单（需要2FA验证） ---

// UpdateMyApiKeyIpWhitelistReq defines the request structure for updating API key IP whitelist with 2FA verification.
type UpdateMyApiKeyIpWhitelistReq struct {
	g.Meta      `path:"/my/apikeys/{apiKeyId}/ip-whitelist" method:"put" tags:"MerchantApiKey" summary:"更新API密钥IP白名单"`
	ApiKeyId    uint   `v:"required#API密钥ID不能为空" dc:"API密钥ID"`
	IpWhitelist string `json:"ipWhitelist" dc:"IP白名单 (逗号分隔)"`
	Code        string `json:"code" v:"required|length:6,6#验证码不能为空|验证码必须为6位" dc:"2FA验证码"`
}

// UpdateMyApiKeyIpWhitelistRes defines the response structure for updating API key IP whitelist.
type UpdateMyApiKeyIpWhitelistRes struct {
	// Success indicates whether the operation was successful.
	Success bool `json:"success" dc:"是否成功"`
}

// --- 商户更新API密钥状态（需要2FA验证） ---

// UpdateMyApiKeyStatusReq defines the request structure for updating API key status with 2FA verification.
type UpdateMyApiKeyStatusReq struct {
	g.Meta   `path:"/my/apikeys/{apiKeyId}/status" method:"put" tags:"MerchantApiKey" summary:"更新API密钥状态"`
	ApiKeyId uint   `v:"required#API密钥ID不能为空" dc:"API密钥ID"`
	Status   string `json:"status" v:"required|in:active,revoked#状态不能为空|状态必须是active或revoked" dc:"状态 (active-启用, revoked-停用)"`
	Code     string `json:"code" v:"required|length:6,6#验证码不能为空|验证码必须为6位" dc:"2FA验证码"`
}

// UpdateMyApiKeyStatusRes defines the response structure for updating API key status.
type UpdateMyApiKeyStatusRes struct {
	// Success indicates whether the operation was successful.
	Success bool `json:"success" dc:"是否成功"`
}

// --- 商户获取API密钥的Secret（需要2FA验证） ---

// GetMyApiKeySecretReq defines the request structure for retrieving API key secret with 2FA verification.
type GetMyApiKeySecretReq struct {
	g.Meta   `path:"/my/apikeys/{apiKeyId}/secret" method:"post" tags:"MerchantApiKey" summary:"获取API密钥Secret"`
	ApiKeyId uint   `v:"required#API密钥ID不能为空" dc:"API密钥ID"`
	Code     string `json:"code" v:"required|length:6,6#验证码不能为空|验证码必须为6位" dc:"2FA验证码"`
}

// GetMyApiKeySecretRes defines the response structure for retrieving API key secret.
type GetMyApiKeySecretRes struct {
	// Secret is the API secret key.
	Secret string `json:"secret" dc:"API密钥Secret"`
}
