package v1

import (
	"merchant-api/api/common"
	"merchant-api/internal/model/entity"

	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
)

// --- 获取我的充值地址列表 ---

// GetMyDepositAddressesReq defines the request structure for getting merchant's own deposit addresses.
type GetMyDepositAddressesReq struct {
	g.Meta `path:"/my/deposit-addresses" method:"get" tags:"MerchantDepositAddresses" summary:"获取我的充值地址列表"`
	common.PageRequest
	TokenName string `json:"tokenName" dc:"币种名称 (筛选特定币种)"`
	Chain     string `json:"chain" dc:"区块链网络 (筛选特定链)"`
	Type      string `json:"type" dc:"地址类型 (筛选特定类型)"`
}

// MerchantDepositAddressInfoType defines the structure for deposit address information in a list.
type MerchantDepositAddressInfoType struct {
	AddressId uint        `json:"addressId" dc:"地址ID"`
	TokenId   uint        `json:"tokenId" dc:"币种ID"`
	TokenName string      `json:"tokenName" dc:"币种名称"`
	Chain     string      `json:"chain" dc:"区块链网络"`
	Address   string      `json:"address" dc:"充值地址"`
	Label     string      `json:"label" dc:"地址标签/备注"`
	Type      string      `json:"type" dc:"地址类型"`
	Image     string      `json:"image" dc:"二维码图片URL"`
	CreatedAt *gtime.Time `json:"createdAt" dc:"创建时间"`
	UpdatedAt *gtime.Time `json:"updatedAt" dc:"更新时间"`
}

// GetMyDepositAddressesRes defines the response structure for getting merchant's own deposit addresses.
type GetMyDepositAddressesRes struct {
	Page common.PageResponse `json:"page" dc:"分页信息"`
	// Data contains the list of deposit addresses.
	Data []*MerchantDepositAddressInfoType `json:"data" dc:"充值地址列表数据"`
}

// --- 获取我的充值地址详情 ---

// GetMyDepositAddressDetailReq defines the request structure for getting specific deposit address details.
type GetMyDepositAddressDetailReq struct {
	g.Meta    `path:"/my/deposit-addresses/{addressId}" method:"get" tags:"MerchantDepositAddresses" summary:"获取我的充值地址详情"`
	AddressId uint `json:"addressId" v:"required#地址ID不能为空" dc:"地址ID"`
}

// MerchantDepositAddressDetailType defines the structure for detailed deposit address information.
type MerchantDepositAddressDetailType struct {
	entity.MerchantAddress
	// 扩展信息
	UsageStats *DepositAddressUsageStats `json:"usageStats" dc:"地址使用统计"`
}

// DepositAddressUsageStats defines the usage statistics for a deposit address.
type DepositAddressUsageStats struct {
	TotalDeposits     int    `json:"totalDeposits" dc:"总充值次数"`
	TotalAmount       string `json:"totalAmount" dc:"总充值金额"`
	LastDepositTime   *gtime.Time `json:"lastDepositTime" dc:"最后充值时间"`
	FirstDepositTime  *gtime.Time `json:"firstDepositTime" dc:"首次充值时间"`
}

// GetMyDepositAddressDetailRes defines the response structure for getting deposit address details.
type GetMyDepositAddressDetailRes struct {
	// Data contains the deposit address details.
	Data *MerchantDepositAddressDetailType `json:"data" dc:"充值地址详情数据"`
}

// --- 获取支持的币种信息 ---

// GetSupportedTokensReq defines the request structure for getting supported tokens for deposit.
type GetSupportedTokensReq struct {
	g.Meta `path:"/my/deposit-addresses/supported-tokens" method:"get" tags:"MerchantDepositAddresses" summary:"获取支持的充值币种信息"`
}

// SupportedTokenInfo defines the structure for supported token information.
type SupportedTokenInfo struct {
	TokenId     uint     `json:"tokenId" dc:"币种ID"`
	Symbol      string   `json:"symbol" dc:"币种符号"`
	Name        string   `json:"name" dc:"币种名称"`
	Description string   `json:"description" dc:"币种描述"`
	Chains      []string `json:"chains" dc:"支持的区块链网络"`
	Enabled     bool     `json:"enabled" dc:"是否启用"`
	MinAmount   string   `json:"minAmount" dc:"最小充值金额"`
	MaxAmount   string   `json:"maxAmount" dc:"最大充值金额"`
}

// GetSupportedTokensRes defines the response structure for getting supported tokens.
type GetSupportedTokensRes struct {
	// Data contains the list of supported tokens.
	Data []*SupportedTokenInfo `json:"data" dc:"支持的币种列表"`
}