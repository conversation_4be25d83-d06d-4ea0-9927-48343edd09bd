package system

import (
	"context"

	"merchant-api/internal/service"

	"github.com/gogf/gf/v2/net/ghttp"

	v1 "merchant-api/api/system/v1"
)

// UpdateMyApiKey implements the logic for merchants to update their own API key.
func (c *ControllerV1) UpdateMyApiKey(ctx context.Context, req *v1.UpdateMyApiKeyReq) (res *v1.UpdateMyApiKeyRes, err error) {
	// 从路径参数中获取 apiKeyId
	r := ghttp.RequestFromCtx(ctx)
	req.ApiKeyId = r.Get("apiKeyId").Uint()
	
	// 直接调用服务层，服务层会从上下文获取商户ID并验证权限
	res, err = service.SystemServiceInstance.UpdateMyApiKey(ctx, req)
	return
}