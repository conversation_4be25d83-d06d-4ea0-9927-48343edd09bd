package system

import (
	"context"

	"merchant-api/internal/service"

	v1 "merchant-api/api/system/v1"
)

// GetMyDepositDetail implements the logic for the GetMyDepositDetail controller.
func (c *ControllerV1) GetMyDepositDetail(ctx context.Context, req *v1.GetMyDepositDetailReq) (res *v1.GetMyDepositDetailRes, err error) {
	res, err = service.SystemServiceInstance.GetMyDepositDetail(ctx, req)
	return
}