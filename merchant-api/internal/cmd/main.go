package cmd

import (
	"context"

	"merchant-api/internal/cmd/common"
	"merchant-api/internal/cmd/cron"
	http "merchant-api/internal/cmd/http"

	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
)

// Main is the main entry point for different services
func Main(ctx context.Context, serviceName string) {
	g.Log().Infof(ctx, "Starting service: %s", serviceName)
	err := gtime.SetTimeZone("Asia/Shanghai")
	if err != nil {
		panic(err)
	}
	// Initialize common services for processor service (which needs wallet operations)
	if serviceName == "processor" {
		if err := common.InitializeServices(ctx); err != nil {
			g.Log().Warningf(ctx, "Failed to initialize common services: %v", err)
			// Continue startup but log warning
		}
	}

	switch serviceName {
	case "http":
		http.StartGateway(ctx)
	case "cron":
		cron.StartCron(ctx)
	default:
		g.Log().Fatalf(ctx, "Unknown service name: %s. Available services: gateway, processor, cron", serviceName)
	}
}
