package v1

import (
	"merchant-api/api/common"
	"merchant-api/internal/model/entity"

	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
	"github.com/shopspring/decimal"
)

// --- 获取授权支付订单列表 ---

// GetAuthPaymentOrderListReq defines the request structure for getting the auth payment order list.
type GetAuthPaymentOrderListReq struct {
	g.Meta `path:"/auth-payment-orders" method:"get" tags:"SystemAuthPaymentOrder" summary:"获取授权支付订单列表"`
	common.PageRequest
	OrderNo          string   `json:"orderNo" dc:"系统订单号 (精确搜索)"`
	MerchantOrderNo  string   `json:"merchantOrderNo" dc:"商户订单号 (精确搜索)"`
	MerchantId       uint64   `json:"merchantId" dc:"商户ID"`
	UserAccount      string   `json:"userAccount" dc:"用户账户标识 (模糊搜索)"`
	OrderType        string   `json:"orderType" dc:"订单类型 (deduct-扣款, add-加款)"`
	TokenSymbol      string   `json:"tokenSymbol" dc:"代币符号"`
	Status           string   `json:"status" dc:"订单状态"`
	CallbackStatus   string   `json:"callbackStatus" dc:"回调状态"`
	MinAmount        string   `json:"minAmount" dc:"最小金额"`
	MaxAmount        string   `json:"maxAmount" dc:"最大金额"`
	DateRange        string   `json:"dateRange" dc:"创建时间范围，格式：2025-01-01,2025-01-31"`
	CreatedAt        []string `json:"createdAt" dc:"创建时间范围数组，格式：[开始时间, 结束时间]"`
	Export           bool     `json:"export" dc:"是否导出 (true-导出, false-不导出)"`
}

// AuthPaymentOrderInfoType defines the structure for auth payment order information in a list.
type AuthPaymentOrderInfoType struct {
	Id                    uint64          `json:"id" dc:"订单ID"`
	OrderNo               string          `json:"orderNo" dc:"系统订单号"`
	MerchantId            uint64          `json:"merchantId" dc:"商户ID"`
	MerchantName          string          `json:"merchantName" dc:"商户名称"`
	UserAccount           string          `json:"userAccount" dc:"用户账户标识"`
	UserId                uint64          `json:"userId" dc:"用户ID"`
	OrderType             string          `json:"orderType" dc:"订单类型"`
	TokenSymbol           string          `json:"tokenSymbol" dc:"代币符号"`
	Amount                decimal.Decimal `json:"amount" dc:"订单金额"`
	AuthReason            string          `json:"authReason" dc:"授权原因"`
	MerchantOrderNo       string          `json:"merchantOrderNo" dc:"商户订单号"`
	MerchantTransactionId uint64          `json:"merchantTransactionId" dc:"商户交易记录ID"`
	UserTransactionId     uint64          `json:"userTransactionId" dc:"用户交易记录ID"`
	Status                string          `json:"status" dc:"订单状态"`
	CallbackStatus        string          `json:"callbackStatus" dc:"回调状态"`
	CallbackAttempts      int             `json:"callbackAttempts" dc:"回调尝试次数"`
	ExpireAt              *gtime.Time     `json:"expireAt" dc:"过期时间"`
	CompletedAt           *gtime.Time     `json:"completedAt" dc:"完成时间"`
	CreatedAt             *gtime.Time     `json:"createdAt" dc:"创建时间"`
	UpdatedAt             *gtime.Time     `json:"updatedAt" dc:"更新时间"`
}

// GetAuthPaymentOrderListRes defines the response structure for getting the auth payment order list.
type GetAuthPaymentOrderListRes struct {
	Page common.PageResponse         `json:"page" dc:"分页信息"`
	Data []*AuthPaymentOrderInfoType `json:"data" dc:"授权支付订单列表数据"`
}

// --- 获取授权支付订单详情 ---

// GetAuthPaymentOrderReq defines the request structure for getting auth payment order details.
type GetAuthPaymentOrderReq struct {
	g.Meta  `path:"/auth-payment-orders/{orderId}" method:"get" tags:"SystemAuthPaymentOrder" summary:"获取授权支付订单详情"`
	OrderId uint64 `json:"orderId" v:"required#订单ID不能为空" dc:"订单ID"`
}

// AuthPaymentOrderDetailType defines the structure for auth payment order details.
type AuthPaymentOrderDetailType struct {
	entity.AuthPaymentOrders
	MerchantName     string                  `json:"merchantName" dc:"商户名称"`
	CallbackHistory  []*CallbackHistoryType  `json:"callbackHistory" dc:"回调历史记录"`
	TransactionInfo  *TransactionInfoType    `json:"transactionInfo" dc:"交易信息"`
}

// CallbackHistoryType defines the structure for callback history records.
type CallbackHistoryType struct {
	AttemptNumber    int         `json:"attemptNumber" dc:"尝试次数"`
	CallbackTime     *gtime.Time `json:"callbackTime" dc:"回调时间"`
	ResponseStatus   int         `json:"responseStatus" dc:"响应状态码"`
	ResponseBody     string      `json:"responseBody" dc:"响应内容"`
	Success          bool        `json:"success" dc:"是否成功"`
}

// TransactionInfoType defines the structure for transaction information.
type TransactionInfoType struct {
	MerchantTransactionNo string          `json:"merchantTransactionNo" dc:"商户交易号"`
	UserTransactionNo     string          `json:"userTransactionNo" dc:"用户交易号"`
	BeforeBalance         decimal.Decimal `json:"beforeBalance" dc:"交易前余额"`
	AfterBalance          decimal.Decimal `json:"afterBalance" dc:"交易后余额"`
	TransactionTime       *gtime.Time     `json:"transactionTime" dc:"交易时间"`
}

// GetAuthPaymentOrderRes defines the response structure for getting auth payment order details.
type GetAuthPaymentOrderRes struct {
	Data *AuthPaymentOrderDetailType `json:"data" dc:"授权支付订单详情数据"`
}

// --- 商户端接口：获取我的授权支付订单列表 ---

// GetMyAuthPaymentOrderListReq defines the request structure for merchants to get their own auth payment order list.
type GetMyAuthPaymentOrderListReq struct {
	g.Meta `path:"/my/auth-payment-orders" method:"get" tags:"MerchantAuthPaymentOrder" summary:"获取我的授权支付订单列表"`
	common.PageRequest
	OrderNo         string   `json:"orderNo" dc:"系统订单号 (精确搜索)"`
	MerchantOrderNo string   `json:"merchantOrderNo" dc:"商户订单号 (精确搜索)"`
	UserAccount     string   `json:"userAccount" dc:"用户账户标识 (模糊搜索)"`
	OrderType       string   `json:"orderType" dc:"订单类型 (deduct-扣款, add-加款)"`
	TokenSymbol     string   `json:"tokenSymbol" dc:"代币符号"`
	Status          string   `json:"status" dc:"订单状态"`
	CallbackStatus  string   `json:"callbackStatus" dc:"回调状态"`
	DateRange       string   `json:"dateRange" dc:"创建时间范围，格式：2025-01-01,2025-01-31"`
	CreatedAt       []string `json:"createdAt" dc:"创建时间范围数组，格式：[开始时间, 结束时间]"`
	Export          bool     `json:"export" dc:"是否导出 (true-导出, false-不导出)"`
}

// GetMyAuthPaymentOrderListRes defines the response structure for merchants to get their own auth payment order list.
type GetMyAuthPaymentOrderListRes struct {
	Page common.PageResponse         `json:"page" dc:"分页信息"`
	Data []*AuthPaymentOrderInfoType `json:"data" dc:"授权支付订单列表数据"`
}

// --- 商户端接口：获取我的授权支付订单详情 ---

// GetMyAuthPaymentOrderReq defines the request structure for merchants to get their own auth payment order details.
type GetMyAuthPaymentOrderReq struct {
	g.Meta  `path:"/my/auth-payment-orders/{orderId}" method:"get" tags:"MerchantAuthPaymentOrder" summary:"获取我的授权支付订单详情"`
	OrderId uint64 `json:"orderId" v:"required#订单ID不能为空" dc:"订单ID"`
}

// GetMyAuthPaymentOrderRes defines the response structure for merchants to get their own auth payment order details.
type GetMyAuthPaymentOrderRes struct {
	Data *AuthPaymentOrderDetailType `json:"data" dc:"授权支付订单详情数据"`
}

// --- 重试授权支付订单回调 ---

// RetryAuthPaymentOrderCallbackReq defines the request structure for retrying auth payment order callback.
type RetryAuthPaymentOrderCallbackReq struct {
	g.Meta  `path:"/auth-payment-orders/{orderId}/callback" method:"post" tags:"SystemAuthPaymentOrder" summary:"重试授权支付订单回调"`
	OrderId uint64 `json:"orderId" v:"required#订单ID不能为空" dc:"订单ID"`
	Notes   string `json:"notes" v:"length:0,200" dc:"备注"`
}

// RetryAuthPaymentOrderCallbackRes defines the response structure for retrying auth payment order callback.
type RetryAuthPaymentOrderCallbackRes struct {
	Success        bool   `json:"success" dc:"是否成功"`
	ResponseStatus int    `json:"responseStatus" dc:"响应状态码"`
	ResponseBody   string `json:"responseBody" dc:"响应内容"`
}

// --- 统计接口 ---

// GetAuthPaymentOrderStatsReq defines the request structure for getting auth payment order statistics.
type GetAuthPaymentOrderStatsReq struct {
	g.Meta      `path:"/auth-payment-orders/stats" method:"get" tags:"SystemAuthPaymentOrder" summary:"获取授权支付订单统计"`
	MerchantId  uint64   `json:"merchantId" dc:"商户ID (可选)"`
	DateRange   string   `json:"dateRange" dc:"统计时间范围，格式：2025-01-01,2025-01-31"`
	StartDate   string   `json:"startDate" dc:"开始日期"`
	EndDate     string   `json:"endDate" dc:"结束日期"`
	GroupBy     string   `json:"groupBy" dc:"分组方式 (day, week, month)"`
}

// AuthPaymentOrderStatsType defines the structure for auth payment order statistics.
type AuthPaymentOrderStatsType struct {
	Date               string                              `json:"date" dc:"日期"`
	TotalOrders        int64                               `json:"totalOrders" dc:"总订单数"`
	DeductOrders       int64                               `json:"deductOrders" dc:"扣款订单数"`
	AddOrders          int64                               `json:"addOrders" dc:"加款订单数"`
	SuccessOrders      int64                               `json:"successOrders" dc:"成功订单数"`
	FailedOrders       int64                               `json:"failedOrders" dc:"失败订单数"`
	PendingOrders      int64                               `json:"pendingOrders" dc:"待处理订单数"`
	TokenAmounts       map[string]decimal.Decimal          `json:"tokenAmounts" dc:"各代币总金额"`
	CallbackStats      *CallbackStatsType                  `json:"callbackStats" dc:"回调统计"`
}

// CallbackStatsType defines the structure for callback statistics.
type CallbackStatsType struct {
	TotalCallbacks    int64 `json:"totalCallbacks" dc:"总回调次数"`
	SuccessCallbacks  int64 `json:"successCallbacks" dc:"成功回调次数"`
	FailedCallbacks   int64 `json:"failedCallbacks" dc:"失败回调次数"`
	PendingCallbacks  int64 `json:"pendingCallbacks" dc:"待回调数"`
	AvgResponseTime   int64 `json:"avgResponseTime" dc:"平均响应时间(ms)"`
}

// GetAuthPaymentOrderStatsRes defines the response structure for getting auth payment order statistics.
type GetAuthPaymentOrderStatsRes struct {
	Data []*AuthPaymentOrderStatsType `json:"data" dc:"统计数据"`
}