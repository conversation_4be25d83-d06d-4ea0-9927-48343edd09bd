package system

import (
	"context"

	"merchant-api/internal/service"

	"github.com/gogf/gf/v2/net/ghttp"

	v1 "merchant-api/api/system/v1"
)

// GetMerchant implements the logic for the GetMerchant controller.
func (c *ControllerV1) GetMerchant(ctx context.Context, req *v1.GetMerchantReq) (res *v1.GetMerchantRes, err error) {
	// 从路径参数中获取 merchantId
	r := ghttp.RequestFromCtx(ctx)
	req.MerchantId = r.Get("merchantId").Uint()
	res, err = service.SystemServiceInstance.GetMerchant(ctx, req)
	return
}
