package system

import (
	"context"

	"merchant-api/internal/service"

	v1 "merchant-api/api/system/v1"
)

// UpdateMyGoogle2FA implements the logic for merchants to enable/disable their Google 2FA.
func (c *ControllerV1) UpdateMyGoogle2FA(ctx context.Context, req *v1.UpdateMyGoogle2FAReq) (res *v1.UpdateMyGoogle2FARes, err error) {
	// 直接调用服务层，服务层会从上下文获取商户ID并验证权限
	res, err = service.SystemServiceInstance.UpdateMyGoogle2FA(ctx, req)
	return
}