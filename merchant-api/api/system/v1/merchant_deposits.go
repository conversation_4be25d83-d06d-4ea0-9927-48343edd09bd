package v1

import (
	"merchant-api/api/common"
	"merchant-api/internal/model/entity"

	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
	"github.com/shopspring/decimal"
)

// --- 获取我的充值记录列表 ---

// GetMyDepositsReq defines the request structure for getting merchant's own deposit records.
type GetMyDepositsReq struct {
	g.Meta `path:"/my/deposits" method:"get" tags:"MerchantDeposits" summary:"获取我的充值记录"`
	common.PageRequest
	TokenId   *uint    `json:"tokenId" dc:"币种ID (筛选特定币种)"`
	TokenName string   `json:"tokenName" dc:"币种名称 (模糊搜索)"`
	State     *uint    `json:"state" dc:"状态 (1-待确认/处理中, 2-已完成/已入账)"`
	TxHash    string   `json:"txHash" dc:"交易哈希 (模糊搜索)"`
	DateRange string   `json:"dateRange" dc:"创建时间范围，格式：2025-01-01,2025-01-31"`
	CreatedAt []string `json:"createdAt" dc:"创建时间范围数组，格式：[开始时间, 结束时间]"`
	Export    bool     `json:"export" dc:"是否导出 (true-导出, false-不导出)"`
}

// MerchantDepositInfoType defines the structure for deposit information in a list.
type MerchantDepositInfoType struct {
	RechargesId          uint            `json:"rechargesId" dc:"充值记录ID"`
	TokenId              uint            `json:"tokenId" dc:"币种ID"`
	TokenName            string          `json:"tokenName" dc:"币种名称"`
	Chan                 string          `json:"chan" dc:"充值渠道"`
	TokenContractAddress string          `json:"tokenContractAddress" dc:"代币合约地址"`
	FromAddress          string          `json:"fromAddress" dc:"来源地址"`
	ToAddress            string          `json:"toAddress" dc:"目标地址"`
	TxHash               string          `json:"txHash" dc:"交易哈希"`
	Amount               decimal.Decimal `json:"amount" dc:"充值数量"`
	State                uint            `json:"state" dc:"状态值"`
	StateText            string          `json:"stateText" dc:"状态描述"`
	FailureReason        string          `json:"failureReason" dc:"失败原因"`
	Confirmations        uint            `json:"confirmations" dc:"确认数"`
	CreatedAt            *gtime.Time     `json:"createdAt" dc:"创建时间"`
	CompletedAt          *gtime.Time     `json:"completedAt" dc:"完成时间"`
	NotificationSent     uint            `json:"notificationSent" dc:"通知状态"`
	NotificationSentAt   *gtime.Time     `json:"notificationSentAt" dc:"通知发送时间"`
}

// GetMyDepositsRes defines the response structure for getting merchant's own deposit records.
type GetMyDepositsRes struct {
	Page common.PageResponse `json:"page" dc:"分页信息"`
	// Data contains the list of deposit records.
	Data []*MerchantDepositInfoType `json:"data" dc:"充值记录列表数据"`
}

// --- 获取我的充值记录详情 ---

// GetMyDepositDetailReq defines the request structure for getting specific deposit record details.
type GetMyDepositDetailReq struct {
	g.Meta      `path:"/my/deposits/{rechargesId}" method:"get" tags:"MerchantDeposits" summary:"获取我的充值记录详情"`
	RechargesId uint `json:"rechargesId" v:"required#充值记录ID不能为空" dc:"充值记录ID"`
}

// MerchantDepositDetailType defines the structure for detailed deposit information.
type MerchantDepositDetailType struct {
	entity.MerchantDeposits
	StateText string `json:"stateText" dc:"状态描述"`
}

// GetMyDepositDetailRes defines the response structure for getting deposit record details.
type GetMyDepositDetailRes struct {
	// Data contains the deposit record details.
	Data *MerchantDepositDetailType `json:"data" dc:"充值记录详情数据"`
}