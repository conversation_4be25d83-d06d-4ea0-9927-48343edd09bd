# 代理IP白名单功能测试说明

## 功能概述

已成功为代理用户添加了IP白名单校验功能。当代理用户登录时，系统会自动检查其IP地址是否在配置的白名单中。

## 实现的功能

### 1. IP白名单校验函数 (`checkAgentIPWhitelist`)
- 查询指定代理的IP白名单配置
- 检查当前客户端IP是否在白名单中
- 如果没有配置白名单，则允许所有IP访问
- 支持启用/禁用状态检查

### 2. 代理用户识别函数 (`getAgentIdFromUsername`)
- 根据用户名查询是否为代理用户
- 返回代理ID，如果不是代理用户则返回0

### 3. 认证中间件增强 (`AuthMiddleware`)
- 在原有认证流程基础上增加IP白名单校验
- 仅对代理用户进行IP白名单检查
- 普通管理员用户不受影响

## 工作流程

1. **用户认证**: 解析JWT Token，获取用户名
2. **用户类型判断**: 查询用户是否为代理用户
3. **IP白名单校验**: 如果是代理用户，检查IP是否在白名单中
4. **访问控制**: 
   - IP在白名单中：允许访问，设置`tokenType=agent`
   - IP不在白名单中：拒绝访问，返回403错误
   - 非代理用户：正常访问，设置`tokenType=admin`

## 数据库查询逻辑

查询条件：
- `agent_id`: 代理ID
- `list_type`: "whitelist" (白名单)
- `use_type`: "agent_login_whitelist" (代理登录白名单)
- `is_enabled`: 1 (启用状态)
- `deleted_at`: NULL (未删除)

## 错误处理

- **数据库查询失败**: 返回500内部错误
- **IP不在白名单**: 返回403禁止访问，提示"当前IP地址不在白名单中，访问被拒绝"
- **系统错误**: 返回500内部错误

## 日志记录

- **调试日志**: IP白名单校验通过
- **警告日志**: IP不在白名单中
- **错误日志**: 数据库查询失败等系统错误

## 测试建议

### 1. 准备测试数据
```sql
-- 创建测试代理
INSERT INTO agents (username, password_hash, agent_name, email, level, status, invitation_code) 
VALUES ('test_agent', 'hashed_password', '测试代理', '<EMAIL>', 1, 1, 'TEST123');

-- 添加IP白名单
INSERT INTO ip_access_list (agent_id, list_type, use_type, ip_address, description, is_enabled) 
VALUES (1, 'whitelist', 'agent_login_whitelist', '*************', '测试IP', 1);
```

### 2. 测试场景
1. **代理用户 + 白名单IP**: 应该允许访问
2. **代理用户 + 非白名单IP**: 应该拒绝访问
3. **代理用户 + 无白名单配置**: 应该允许访问
4. **非代理用户**: 应该正常访问，不受IP限制

### 3. 验证方法
- 检查HTTP响应状态码
- 检查返回的错误消息
- 检查日志输出
- 检查上下文变量设置（`tokenType`, `agentId`）

## 配置说明

无需额外配置，功能会自动根据数据库中的白名单配置生效。

## 注意事项

1. 如果代理没有配置任何IP白名单，则允许所有IP访问
2. 只有启用状态的白名单条目才会生效
3. IP地址必须完全匹配（不支持CIDR或通配符）
4. 功能仅对代理用户生效，不影响普通管理员用户
