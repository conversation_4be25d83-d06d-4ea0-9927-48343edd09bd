package casdoor

import (
	"merchant-api/internal/consts"
	"merchant-api/internal/dao"
	"context"
	"crypto/md5"
	"encoding/hex"

	"github.com/casdoor/casdoor-go-sdk/casdoorsdk"
	"github.com/gogf/gf/v2/frame/g"
)

// SyncMenusWithCasdoor 将菜单同步到Casdoor作为权限
func SyncMenusWithCasdoor(ctx context.Context, existingPermissions []*casdoorsdk.Permission) {
	// 创建权限映射，便于快速查找
	permissionMap := make(map[string]*casdoorsdk.Permission)
	for _, perm := range existingPermissions {
		permissionMap[perm.Name] = perm
	}
	// 1. 从数据库获取所有菜单
	allMenus, err := dao.MerchantMenu.GetAllMenus(ctx, g.Map{
		dao.MerchantMenu.Columns().Status: consts.StatusEnabled,
	})
	if err != nil {
		g.Log().Errorf(ctx, "获取菜单列表失败: %v", err)
		return
	}

	if len(allMenus) == 0 {
		g.Log().Info(ctx, "没有菜单数据，无需同步菜单权限")
		return
	}

	g.Log().Infof(ctx, "获取到菜单 %d 条", len(allMenus))

	// 2. 往Casdoor添加菜单权限
	permissionsToAdd := []*casdoorsdk.Permission{}

	// 用于跟踪统计信息
	var totalScanned int
	var createdCount int
	var existingCount int
	var skippedCount int

	// 遍历所有菜单，生成权限对象
	for _, m := range allMenus {
		// 假设菜单结构有 id, name, path 字段
		totalScanned++

		menuID := m.Id

		// 确保 menuName 和 menuPath 字段存在并是字符串类型
		menuName := m.Name
		if menuName == "" {
			g.Log().Warningf(ctx, "菜单 [ID: %v] 的name字段无效，跳过权限同步", menuID)
			skippedCount++
			continue
		}

		menuPath := m.Path
		if menuPath == "" {
			g.Log().Debugf(ctx, "菜单 [ID: %v, Name: %s] Path 为空，跳过权限同步", menuID, menuName)
			skippedCount++
			continue
		}

		// 使用下划线替代冒号，避免Casdoor解析错误
		// 格式为: "menu_PATH"
		// Convert to PascalCase (capitalized camel case)
		// parts := strings.Split(strings.ReplaceAll(menuPath, "/", " "), " ")
		// for i, part := range parts {
		// 	if part != "" {
		// 		parts[i] = strings.ToUpper(part[:1]) + part[1:]
		// 	}
		// }
		// permissionKey := "Menu" + strings.Join(parts, "")
		hash := md5.Sum([]byte(menuPath))

		// 创建权限对象
		permission := &casdoorsdk.Permission{
			Owner:       GlobalConfig.Server.Owner,
			Name:        hex.EncodeToString(hash[:]), //计算 md5 作为权限名称
			DisplayName: "Menu " + menuName,
			Description: "Menu: " + menuName + " (Path: " + menuPath + ")",
			Users:       []string{GlobalConfig.Server.User}, // 指定用户
			// Groups:      []string{},
			Roles:   []string{GlobalConfig.Server.Role}, // 指定角色 后期增加手动进行
			Domains: []string{},                         //域
			Model:   GlobalConfig.Server.Model,          // 使用内置模型
			// Adapter:      "",                 //
			ResourceType: "Custom",           // 资源类型为Menu
			Resources:    []string{menuPath}, // 使用菜单路径作为资源标识符
			Actions:      []string{"Visit"},  // 菜单操作为访问
			Effect:       "Allow",            // 允许访问
			IsEnabled:    true,               // 启用权限
		}

		// 添加到权限集合
		permissionsToAdd = append(permissionsToAdd, permission)
	}

	g.Log().Infof(ctx, "发现 %d 条菜单需要同步到Casdoor权限", len(permissionsToAdd))

	if len(permissionsToAdd) > 0 {
		g.Log().Info(ctx, "开始将菜单同步到Casdoor权限....")
		for _, permission := range permissionsToAdd {
			// 从权限映射中检查权限是否已存在
			existingPermission := permissionMap[permission.Name]

			if existingPermission != nil {
				// 权限已存在
				// g.Log().Infof(ctx, "菜单权限 %s/%s 已存在，跳过添加", permission.Owner, permission.Name)
				existingCount++
				// 如果需要更新已存在的权限，可以在这里添加逻辑
				// 例如: 更新DisplayName、Description等
				// casdoorsdk.UpdatePermission(permission)
			} else {
				// 权限不存在，添加新权限
				// g.Log().Infof(ctx, "菜单权限 %s/%s 不存在，将添加新权限", permission.Owner, permission.Name)
				success, err := casdoorsdk.AddPermission(permission)
				if err != nil {
					g.Log().Errorf(ctx, "添加Casdoor菜单权限 %s/%s 失败: %v", permission.Owner, permission.Name, err)
				} else if !success {
					g.Log().Errorf(ctx, "添加Casdoor菜单权限 %s/%s 失败 (success=false)", permission.Owner, permission.Name)
				} else {
					// g.Log().Infof(ctx, "成功添加Casdoor菜单权限: %s/%s", permission.Owner, permission.Name)
					createdCount++
					// 添加成功后更新映射，以便同一批处理中的后续检查
					permissionMap[permission.Name] = permission
				}
			}
		}
		g.Log().Info(ctx, "Casdoor 菜单权限同步完成")
	}

	g.Log().Infof(ctx, "菜单权限同步统计: 总扫描 %d, 新创建 %d, 已存在 %d, 跳过 %d",
		totalScanned, createdCount, existingCount, skippedCount)
}
