package system

import (
	"context"

	"merchant-api/internal/service"

	"github.com/gogf/gf/v2/net/ghttp"

	v1 "merchant-api/api/system/v1"
)

// ResetMerchantPassword implements the logic for the ResetMerchantPassword controller.
func (c *ControllerV1) ResetMerchantPassword(ctx context.Context, req *v1.ResetMerchantPasswordReq) (res *v1.ResetMerchantPasswordRes, err error) {
	// 从路径参数中获取 merchantId
	r := ghttp.RequestFromCtx(ctx)
	req.MerchantId = r.Get("merchantId").Uint()
	res, err = service.SystemServiceInstance.ResetMerchantPassword(ctx, req)
	return
}
