package system

import (
	"context"

	"merchant-api/internal/service"

	v1 "merchant-api/api/system/v1"
)

// ResetMyGoogle2FA implements the logic for merchants to reset their Google 2FA.
func (c *ControllerV1) ResetMyGoogle2FA(ctx context.Context, req *v1.ResetMyGoogle2FAReq) (res *v1.ResetMyGoogle2FARes, err error) {
	// 直接调用服务层，服务层会从上下文获取商户ID并验证权限
	res, err = service.SystemServiceInstance.ResetMyGoogle2FA(ctx, req)
	return
}