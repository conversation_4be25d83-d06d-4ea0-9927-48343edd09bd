package system

import (
	"context"

	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/frame/g"

	"merchant-api/api/system/v1"
	"merchant-api/internal/codes"
	"merchant-api/internal/service"
)

func (c *ControllerV1) ResetMyWebhookSecret(ctx context.Context, req *v1.ResetMyWebhookSecretReq) (res *v1.ResetMyWebhookSecretRes, err error) {
	res = &v1.ResetMyWebhookSecretRes{}

	// 从上下文获取当前商户ID和用户名
	var currentMerchantId uint
	if r := g.RequestFromCtx(ctx); r != nil {
		currentMerchantId = r.GetCtxVar("merchantId").Uint()
		if currentMerchantId == 0 {
			return nil, gerror.NewCode(codes.CodeUnauthorized, "未登录或非商户用户")
		}
	} else {
		return nil, gerror.NewCode(codes.CodeUnauthorized, "无法获取请求上下文")
	}


	// 创建webhook服务实例
	webhookService := service.NewMerchantWebhookService()

	// 重置webhook密钥
	newSecret, err := webhookService.ResetMyWebhookSecret(ctx, currentMerchantId,  req.Code)
	if err != nil {
		return nil, gerror.Wrap(err, "重置webhook密钥失败")
	}

	res.WebhookSecret = newSecret
	return res, nil
}
