package system

import (
	"context"

	"merchant-api/internal/service"

	v1 "merchant-api/api/system/v1"
)

// GetMyProfile implements the logic for merchants to get their own profile.
func (c *ControllerV1) GetMyProfile(ctx context.Context, req *v1.GetMyProfileReq) (res *v1.GetMyProfileRes, err error) {
	// 直接调用服务层，服务层会从上下文获取商户ID
	res, err = service.SystemServiceInstance.GetMyProfile(ctx, req)
	return
}