package v1

import (
	"merchant-api/api/common"
	"merchant-api/internal/model/entity"

	"github.com/gogf/gf/v2/frame/g"
)

// MenuTreeNode represents a node in the menu tree structure.
type MenuTreeNode struct {
	*entity.MerchantMenu
	// Children contains the child menu nodes.
	Children []*MenuTreeNode `json:"children"`
}

// --- 获取菜单列表 ---

// GetMenuListReq defines the request structure for getting the menu list.
type GetMenuListReq struct {
	g.Meta `path:"/menus" method:"get" tags:"SystemMenu" summary:"获取菜单列表"`
	common.PageRequest
	Name      string `json:"name" dc:"菜单名称 (模糊查询)"`
	Status    *int   `json:"status" dc:"菜单状态 (0:禁用, 1:启用)"` // 使用指针区分未传和传0
	DateRange string `json:"dateRange" dc:"创建时间范围，格式：2025-01-01,2025-01-31"`
}

// GetMenuListRes defines the response structure for getting the menu list.
type GetMenuListRes struct {
	// Page contains the pagination information.
	Page common.PageResponse `json:"page" dc:"分页信息"`
	// Data contains the menu tree list.
	Data []*MenuTreeNode `json:"data" dc:"菜单树形列表"`
}

// 获取全部菜单
type GetAllMenuListReq struct {
	//g.Meta `path:"/menus/all" method:"get" tags:"SystemMenu" summary:"获取全部菜单"`
}

type GetAllMenuListRes struct {
	Data []*MenuTreeNode `json:"data" dc:"菜单树形列表"`
}

// --- 获取当前用户可访问菜单 ---

// GetUserAccessibleMenusReq defines the request for getting user's accessible menus.
type GetUserAccessibleMenusReq struct {
	//g.Meta `path:"/menus/accessible" method:"get" tags:"SystemMenu" summary:"获取当前用户可访问的菜单树"`
	// 通常不需要请求参数，用户身份从 token/session 中获取
}

// GetUserAccessibleMenusRes defines the response for user's accessible menus.
type GetUserAccessibleMenusRes struct {
	Data []*MenuTreeNode `json:"data" dc:"用户可访问的菜单树"`
}

// --- 新增菜单 ---

// AddMenuReq defines the request structure for adding a new menu item.
type AddMenuReq struct {
	//g.Meta             `path:"/menus" method:"post" tags:"SystemMenu" summary:"新增菜单"`
	Pid                int64  `json:"pid" v:"required" dc:"父菜单ID (0为根目录)"`
	Name               string `json:"name" v:"required|length:1,100#菜单名称不能为空|名称长度限制1-100" dc:"菜单名称 (建议唯一，支持中英文)"`
	Path               string `json:"path" v:"length:0,200" dc:"路由路径 (目录/菜单需要)"` // 路由路径，目录和菜单类型需要
	HideInMenu         int    `json:"hideInMenu" v:"in:0,1#是否在菜单中隐藏" dc:"是否在菜单中隐藏 (0: 显示, 1: 隐藏)"`
	HideChildrenInMenu int    `json:"hideChildrenInMenu" v:"in:0,1#是否在子菜单中隐藏" dc:"是否在子菜单中隐藏 (0: 显示, 1: 隐藏)"`
	Target             string `json:"target" v:"length:0,50" dc:"跳转目标 (如：_blank, _self等)"`
	Access             string `json:"access" v:"length:0,50" dc:"权限标识 (如：admin:menu:add等)"` // 权限标识
	Key                string `json:"key" v:"length:0,50" dc:"菜单唯一标识 (建议唯一，英文)"`            // 菜单唯一标识
	Icon               string `json:"icon" v:"length:0,50" dc:"菜单图标"`
	Sort               int    `json:"sort" v:"required|min:0#排序值不能为空|排序值不能为负" d:"0" dc:"排序"`
	Remark             string `json:"remark" v:"length:0,200" dc:"备注"`
	Status             int    `json:"status" v:"required|in:0,1#状态不能为空|状态值必须是0或1" d:"1" dc:"菜单状态(0:禁用, 1:启用)"`
}

// AddMenuRes defines the response structure after adding a new menu item.
type AddMenuRes struct {
	// Id is the ID of the newly created menu item.
	Id int64 `json:"id" dc:"新增的菜单ID"`
}

// --- 编辑菜单 ---

// EditMenuReq defines the request structure for editing an existing menu item.
type EditMenuReq struct {
	g.Meta             `path:"/menus/{id}" method:"put" tags:"SystemMenu" summary:"编辑菜单"`
	Id                 int64  `json:"id" v:"required#菜单ID不能为空" dc:"菜单ID"` // 从路径获取
	Pid                int64  `json:"pid" v:"required" dc:"父菜单ID (0为根目录)"`
	Name               string `json:"name" v:"required|length:1,100#菜单名称不能为空|名称长度限制1-100" dc:"菜单名称 (建议唯一，支持中英文)"`
	Path               string `json:"path" v:"length:0,200" dc:"路由路径 (目录/菜单需要)"` // 路由路径，目录和菜单类型需要
	HideInMenu         int    `json:"hideInMenu" v:"in:0,1#是否在菜单中隐藏" dc:"是否在菜单中隐藏 (0: 显示, 1: 隐藏)"`
	HideChildrenInMenu int    `json:"hideChildrenInMenu" v:"in:0,1#是否在子菜单中隐藏" dc:"是否在子菜单中隐藏 (0: 显示, 1: 隐藏)"`
	Target             string `json:"target" v:"length:0,50" dc:"跳转目标 (如：_blank, _self等)"`
	Access             string `json:"access" v:"length:0,50" dc:"权限标识 (如：admin:menu:add等)"` // 权限标识
	Key                string `json:"key" v:"length:0,50" dc:"菜单唯一标识 (建议唯一，英文)"`            // 菜单唯一标识
	Icon               string `json:"icon" v:"length:0,50" dc:"菜单图标"`
	Sort               int    `json:"sort" v:"required|min:0#排序值不能为空|排序值不能为负" d:"0" dc:"排序"`
	Remark             string `json:"remark" v:"length:0,200" dc:"备注"`
	Status             int    `json:"status" v:"required|in:0,1#状态不能为空|状态值必须是0或1" d:"1" dc:"菜单状态(0:禁用, 1:启用)"`
}

// EditMenuRes defines the response structure after editing a menu item.
type EditMenuRes struct {
	// Typically empty on success.
}

// --- 删除菜单 ---

// DeleteMenuReq defines the request structure for deleting a menu item.
type DeleteMenuReq struct {
	//g.Meta `path:"/menus/{id}" method:"delete" tags:"SystemMenu" summary:"删除菜单"`
	Id int64 `json:"id" v:"required#菜单ID不能为空" dc:"菜单ID"` // 从路径获取
}

// DeleteMenuRes defines the response structure after deleting a menu item.
type DeleteMenuRes struct {
	// Typically empty on success.
}
