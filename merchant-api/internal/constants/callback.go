package constants

// 回调事件类型定义
const (
	// 充值事件
	CallbackEventDepositConfirmed = "deposit_confirmed" // 充值确认（状态=2）

	// 提现事件
	CallbackEventWithdrawCreated   = "withdraw_created"   // 提现创建（状态=1）
	CallbackEventWithdrawCancelled = "withdraw_cancelled" // 提现撤销（状态=6）
	CallbackEventWithdrawApproved  = "withdraw_approved"  // 提现通过（状态=2）
	CallbackEventWithdrawRejected  = "withdraw_rejected"  // 提现拒绝（状态=3）
	CallbackEventWithdrawCompleted = "withdraw_completed" // 提现完成（状态=4）

	// 授权支付事件
	CallbackEventAuthPayment         = "auth_payment"     // 授权支付回调（通用）
	CallbackEventAuthAddCompleted    = "add_completed"    // 授权加款完成
	CallbackEventAuthDeductCompleted = "deduct_completed" // 授权扣款完成
)

// EnabledCallbackEvents 需要发送回调的事件类型（在代码中定义，而非数据库配置）
var EnabledCallbackEvents = map[string]bool{
	CallbackEventDepositConfirmed:    true,
	CallbackEventWithdrawCreated:     true,
	CallbackEventWithdrawCancelled:   true,
	CallbackEventWithdrawApproved:    true,
	CallbackEventWithdrawRejected:    true,
	CallbackEventWithdrawCompleted:   true,
	CallbackEventAuthPayment:         true,
	CallbackEventAuthAddCompleted:    true,
	CallbackEventAuthDeductCompleted: true,
}

// IsCallbackEventEnabled 检查事件类型是否需要发送回调
func IsCallbackEventEnabled(eventType string) bool {
	return EnabledCallbackEvents[eventType]
}
