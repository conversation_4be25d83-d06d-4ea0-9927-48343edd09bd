package cron

import (
	"context"

	"github.com/gogf/gf/v2/frame/g"

	"merchant-api/internal/task"
)

// StartCron 启动定时任务服务
func StartCron(ctx context.Context) {
	g.Log().Info(ctx, "Initializing Cron Service...")

	// 初始化必要的服务（包括配置和钱包管理器）
	// boot.Initialize(ctx)

	// 使用 task 模块统一管理所有定时任务
	if err := task.StartTasks(ctx); err != nil {
		g.Log().Fatalf(ctx, "Failed to start task scheduler: %v", err)
	}

	// 阻塞 Cron Service goroutine
	g.Log().Info(ctx, "Cron Service initialization complete. Running...")
	select {}
}
