package v1

import (
	"time"

	"github.com/gogf/gf/v2/frame/g"
)

// --- 测试回调接口 ---

// TestCallbackReq defines the request structure for testing callback URL.
type TestCallbackReq struct {
	g.Meta       `path:"/test-callback" method:"post" tags:"CallbackTest" summary:"测试回调URL"`
	CallbackType string      `json:"callbackType" v:"required|in:withdraw_success,deposit_success" dc:"回调类型: withdraw_success-提现成功, deposit_success-充值成功"`
	MockData     interface{} `json:"mockData,omitempty" dc:"自定义测试数据 (可选，不提供则使用默认mock数据)"`
}

// TestCallbackRes defines the response structure for testing callback URL.
type TestCallbackRes struct {
	g.Meta       `mime:"application/json"`
	Success      bool              `json:"success" dc:"测试是否成功"`
	Message      string            `json:"message" dc:"测试结果消息"`
	CallbackInfo *CallbackTestInfo `json:"callbackInfo" dc:"详细的回调测试信息"`
}

// CallbackTestInfo contains detailed information about the callback test.
type CallbackTestInfo struct {
	// 请求信息
	RequestURL     string            `json:"requestUrl" dc:"回调请求URL"`
	RequestMethod  string            `json:"requestMethod" dc:"请求方法"`
	RequestHeaders map[string]string `json:"requestHeaders" dc:"请求头信息"`
	RequestBody    string            `json:"requestBody" dc:"请求体内容"`

	// 签名调试信息
	SignatureInfo *SignatureDebugInfo `json:"signatureInfo" dc:"签名调试信息"`

	// 响应信息
	ResponseStatus int    `json:"responseStatus" dc:"HTTP响应状态码"`
	ResponseBody   string `json:"responseBody" dc:"响应体内容"`
	ResponseTime   string `json:"responseTime" dc:"响应时间"`

	// 测试结果
	Success      bool   `json:"success" dc:"回调是否成功"`
	ErrorMessage string `json:"errorMessage,omitempty" dc:"错误信息"`

	// 测试时间
	TestedAt time.Time `json:"testedAt" dc:"测试执行时间"`
}

// SignatureDebugInfo contains signature debugging information.
type SignatureDebugInfo struct {
	Timestamp  string `json:"timestamp" dc:"时间戳"`
	Nonce      string `json:"nonce" dc:"随机数"`
	SignString string `json:"signString" dc:"签名字符串 (method+uri+timestamp+nonce+body)"`
	Signature  string `json:"signature" dc:"生成的HMAC-SHA256签名"`
	APIKey     string `json:"apiKey" dc:"使用的API密钥"`
	SecretSalt string `json:"secretSalt,omitempty" dc:"签名密钥 (调试用，生产环境应隐藏)"`
}

// --- Mock数据结构定义 ---

// MockWithdrawData defines the structure for mock withdraw success data.
type MockWithdrawData struct {
	EventType    string `json:"eventType" dc:"事件类型: withdraw_success"`
	OrderNo      string `json:"orderNo" dc:"测试订单号"`
	MerchantId   uint64 `json:"merchantId" dc:"商户ID"`
	Amount       string `json:"amount" dc:"申请金额"`
	Currency     string `json:"currency" dc:"币种"`
	ActualAmount string `json:"actualAmount" dc:"实际到账金额"`
	HandlingFee  string `json:"handlingFee" dc:"手续费"`
	TxHash       string `json:"txHash" dc:"交易哈希"`
	CompletedAt  string `json:"completedAt" dc:"完成时间"`
	Timestamp    int64  `json:"timestamp" dc:"时间戳"`
}

// MockDepositData defines the structure for mock deposit success data.
type MockDepositData struct {
	EventType     string `json:"eventType" dc:"事件类型: deposit_success"`
	OrderNo       string `json:"orderNo" dc:"测试订单号"`
	MerchantId    uint64 `json:"merchantId" dc:"商户ID"`
	Amount        string `json:"amount" dc:"充值金额"`
	Currency      string `json:"currency" dc:"币种"`
	FromAddress   string `json:"fromAddress" dc:"来源地址"`
	ToAddress     string `json:"toAddress" dc:"目标地址"`
	TxHash        string `json:"txHash" dc:"交易哈希"`
	Confirmations int    `json:"confirmations" dc:"确认数"`
	CompletedAt   string `json:"completedAt" dc:"完成时间"`
	Timestamp     int64  `json:"timestamp" dc:"时间戳"`
}