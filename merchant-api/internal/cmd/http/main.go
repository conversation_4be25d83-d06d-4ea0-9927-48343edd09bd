package cmd

import (
	"merchant-api/internal/boot"
	"merchant-api/internal/router"
	"merchant-api/utility/swagger"
	"context"
	"os"
	"os/signal"
	"syscall"
	"time"

	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gcmd"
	"github.com/gogf/gf/v2/os/glog"
)

// StartGateway starts the HTTP gateway service
func StartGateway(ctx context.Context) {
	s := g.Server()

	// 注册所有路由
	router.AdminV1(ctx, s.Group("/"))
	// 初始化swagger
	s.SetSwaggerUITemplate(swagger.MySwaggerUITemplate)

	// 非阻塞方式启动HTTP服务
	if startErr := s.Start(); startErr != nil {
		glog.Fatalf(ctx, "HTTP Server start failed: %v", startErr)
		return
	}
	// ---- 服务启动后执行的逻辑 ----
	// 此时 HTTP 服务已经开始监听（如果 s.Start() 没有报错）
	// 在这里可以执行例如服务注册、初始化其他依赖于HTTP服务的模块等操作
	// performPostStartTasks()
	// --------------------------
	boot.Initialize(ctx)

	// 由于 s.Start() 是非阻塞的，主 goroutine 在执行完上面的代码后会退出，
	// 导致HTTP服务也停止。因此需要一种方式来保持程序运行。
	// GoFrame 提供了 gcmd.WaitSignal() 来等待中断信号，实现优雅退出。
	// 或者可以使用 select {} 来永久阻塞。
	glog.Info(ctx, "Application is now running. Press CTRL+C to exit.")

	// 2. 等待中断信号以实现优雅关闭
	quit := make(chan os.Signal, 1)
	// SIGHUP: 终端控制进程结束(终端连接断开)
	// SIGINT: 用户发送INTR字符(Ctrl+C)触发
	// SIGQUIT: 用户发送QUIT字符(Ctrl+/)触发
	// SIGTERM: 程序结束(terminate)信号，与SIGKILL不同的是该信号可以被阻塞和处理
	signal.Notify(quit, syscall.SIGHUP, syscall.SIGINT, syscall.SIGQUIT, syscall.SIGTERM)
	glog.Infof(ctx, "Application is now running. Press CTRL+C or send kill signal to exit.")

	// 阻塞等待信号
	sig := <-quit
	glog.Infof(ctx, "Received signal: %s. Application is shutting down...", sig.String())

	// 3. 执行服务关闭操作
	// 可以设置一个超时上下文，以防 Shutdown 过程卡住
	shutdownCtx, cancel := context.WithTimeout(context.Background(), 30*time.Second) // 例如30秒超时
	defer cancel()

	if err := s.Shutdown(); err != nil {
		glog.Errorf(shutdownCtx, "HTTP Server shutdown error: %v", err)
	} else {
		glog.Info(shutdownCtx, "HTTP Server has been shut down gracefully.")
	}
}

var (
	Main = gcmd.Command{
		Name:  "main",
		Usage: "main",
		Brief: "start http server",
		Func: func(ctx context.Context, parser *gcmd.Parser) (err error) {
			StartGateway(ctx)
			return nil
		},
	}
)
