# 2FA实现测试指南

## 问题解决

用户首次登录时没有返回2FA密钥和二维码信息的问题已经解决。

### 修改内容

1. **扩展了CasdoorSigninRes结构体**：
   - 添加了`RequireSetup2FA`字段：指示是否需要设置2FA
   - 添加了`Require2FA`字段：指示是否需要验证2FA
   - 添加了`TotpSecret`字段：TOTP密钥（仅首次设置时返回）
   - 添加了`QRCodeURL`字段：二维码URL
   - 添加了`MerchantId`字段：商户ID

2. **修改了CasdoorSignin登录逻辑**：
   - 查询商户信息检查2FA状态
   - 检查账户锁定状态
   - 根据`google2fa_enabled`字段判断是否需要设置或验证2FA
   - 生成TOTP密钥和二维码URL（首次设置时）
   - 缓存临时认证信息

3. **添加了新的API接口**：
   - `POST /api/system/auth/2fa/setup`：设置2FA
   - `POST /api/system/auth/2fa/verify`：验证2FA

4. **添加了错误码**：
   - `Code2FARequired`：需要进行2FA验证
   - `Code2FAInvalid`：2FA验证码无效
   - `Code2FASetupRequired`：需要设置2FA
   - `Code2FATempTokenInvalid`：临时认证信息无效
   - `Code2FALocked`：2FA验证失败次数过多

## 测试流程

### 1. 首次登录（需要设置2FA）

**请求**：
```bash
curl -X POST http://localhost:8002/api/system/auth/casdoor/signin \
  -H "Content-Type: application/json" \
  -d '{
    "code": "78ff0ad42770c7320ee4",
    "state": "q51f3mel1u"
  }'
```

**期望响应**：
```json
{
  "code": 0,
  "data": {
    "accessToken": {
      "access_token": "...",
      "token_type": "Bearer",
      "refresh_token": "...",
      "expiry": "2025-07-02T20:52:20.27882115+08:00"
    },
    "requireSetup2FA": true,
    "require2FA": false,
    "totpSecret": "YWLL3DA7SZMH5S2XSHRX754MDILUU4BZ",
    "qrCodeURL": "otpauth://totp/<EMAIL>?secret=YWLL3DA7SZMH5S2XSHRX754MDILUU4BZ&issuer=MerchantAPI",
    "merchantId": 1
  }
}
```

### 2. 设置2FA

**请求**：
```bash
curl -X POST http://localhost:8002/api/system/auth/2fa/setup \
  -H "Content-Type: application/json" \
  -d '{
    "code": "123456",
    "merchantId": 1
  }'
```

**期望响应**：
```json
{
  "code": 0,
  "data": {
    "token": "eyJhbGciOiJSUzI1NiIs...",
    "expireAt": 1751460738,
    "recoveryCodes": [
      "4373-0529",
      "3204-1873",
      "6475-1092",
      "0006-0535",
      "8014-0272",
      "6385-4557",
      "0543-6469",
      "5703-5400",
      "9167-2626",
      "8355-1871"
    ]
  }
}
```

### 3. 已启用2FA的用户登录

**第一步 - Casdoor登录**：
```bash
curl -X POST http://localhost:8002/api/system/auth/casdoor/signin \
  -H "Content-Type: application/json" \
  -d '{
    "code": "new_code",
    "state": "new_state"
  }'
```

**期望响应**：
```json
{
  "code": 0,
  "data": {
    "accessToken": {
      "access_token": "...",
      "token_type": "Bearer",
      "refresh_token": "...",
      "expiry": "2025-07-02T20:52:20.27882115+08:00"
    },
    "requireSetup2FA": false,
    "require2FA": true,
    "merchantId": 1
  }
}
```

**第二步 - 2FA验证**：
```bash
curl -X POST http://localhost:8002/api/system/auth/2fa/verify \
  -H "Content-Type: application/json" \
  -d '{
    "code": "654321",
    "merchantId": 1
  }'
```

**期望响应**：
```json
{
  "code": 0,
  "data": {
    "token": "eyJhbGciOiJSUzI1NiIs...",
    "expireAt": 1751460738
  }
}
```

## 数据库变化

设置2FA后，数据库中的商户记录会更新：
- `google2fa_enabled` = 1
- `google2fa_secret` = 生成的TOTP密钥
- `recovery_codes` = JSON格式的恢复码数组

## 安全特性

1. **防暴力破解**：连续5次验证失败会锁定账户30分钟
2. **临时缓存**：设置和验证过程中的临时信息缓存15分钟
3. **恢复码**：提供10个一次性恢复码
4. **IP记录**：记录最后登录IP和时间

## 前端集成

前端需要根据响应中的标志位进行不同处理：
- `requireSetup2FA=true`：显示2FA设置页面，包含二维码
- `require2FA=true`：显示2FA验证页面
- 两者都为false：直接登录成功
