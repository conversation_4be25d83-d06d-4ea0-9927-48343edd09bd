package casdoor

import (
	"merchant-api/internal/utility"
	"context"
	"crypto/md5"
	"encoding/hex"
	"strings"

	"github.com/casdoor/casdoor-go-sdk/casdoorsdk"
	"github.com/gogf/gf/v2/frame/g"
)

// SyncRoutesWithCasdoor 将注册的路由同步到Casdoor作为权限
func SyncRoutesWithCasdoor(ctx context.Context, existingPermissions []*casdoorsdk.Permission) {
	// 创建权限映射，便于快速查找
	permissionMap := make(map[string]*casdoorsdk.Permission)
	for _, perm := range existingPermissions {
		permissionMap[perm.Name] = perm
	}
	// 1. 获取所有API信息
	apis, err := utility.GetAllAPIs()
	if err != nil {
		g.Log().Error(ctx, err)
		return
	}

	g.Log().Infof(ctx, "获取到HTTP路由 %d 条", len(apis))

	if len(apis) == 0 {
		g.Log().Info(ctx, "没有已注册的HTTP路由，无需同步API权限")
		return
	}

	// 遍历所有路由，创建Casdoor权限
	permissionsToAdd := []*casdoorsdk.Permission{}

	for _, api := range apis {
		// 跳过一些内部路由或不需要同步的路由
		if api.Meta == nil || api.Meta.Path == "" {
			continue
		}

		if strings.HasPrefix(api.Meta.Path, "/swagger") ||
			strings.HasPrefix(api.Meta.Path, "/debug") {
			continue
		}

		// g.Log().Debugf(ctx, "Processing API: %v", api)

		// 转换为PascalCase (驼峰命名，首字母大写)
		// 格式为: "Perm + Method + Path"
		parts := strings.Split(strings.ReplaceAll(api.Meta.Path, "/", " "), " ")
		for i, part := range parts {
			if part != "" {
				parts[i] = strings.ToUpper(part[:1]) + part[1:]
			}
		}

		// 方法名转为首字母大写
		// methodName := strings.ToUpper(api.Meta.Method[:1]) + strings.ToLower(api.Meta.Method[1:])

		// permissionKey := uuid.New().String()

		displayName := api.Meta.Summary
		if len(displayName) > 100 { // 限制DisplayName长度为100字符
			displayName = displayName[:97] + "..."
		}
		resources := api.Meta.Path + "@" + api.Meta.Method
		// 创建权限对象
		hash := md5.Sum([]byte(resources))
		permission := &casdoorsdk.Permission{
			Owner:       GlobalConfig.Server.Owner,
			Name:        hex.EncodeToString(hash[:]), //计算 md5 作为权限名称
			DisplayName: "API " + displayName,
			Description: "API: " + api.Meta.Path,
			Users:       []string{GlobalConfig.Server.User}, // 指定用户
			// Users:       []string{GlobalConfig.Server.User}, // 指定用户
			// Groups:      []string{},
			Roles:   []string{GlobalConfig.Server.Role}, // 指定角色 后期增加手动进行
			Domains: []string{},                         //域
			Model:   GlobalConfig.Server.Model,          // 使用内置模型
			// Adapter:      "",                 //
			ResourceType: "Custom",            // 资源类型为Menu
			Resources:    []string{resources}, // 使用菜单路径作为资源标识符
			Actions:      []string{"Visit"},   // 菜单操作为访问
			Effect:       "Allow",             // 允许访问
			IsEnabled:    true,                // 启用权限
		}

		// 添加到权限集合
		permissionsToAdd = append(permissionsToAdd, permission)
	}

	g.Log().Infof(ctx, "发现 %d 条路由需要同步到Casdoor权限", len(permissionsToAdd))

	if len(permissionsToAdd) > 0 {
		g.Log().Info(ctx, "开始将API路由同步到Casdoor权限....")
		for _, permission := range permissionsToAdd {
			// g.Log().Debugf(ctx, "Processing permission. Description: '%s'. Attempting to create Casdoor permission: %s/%s", permission.Description, permission.Owner, permission.Name) // DEBUG: Log which route is being processed

			// 从权限映射中检查权限是否已存在
			existingPermission := permissionMap[permission.Name]

			if existingPermission != nil {
				// 权限已存在
				// g.Log().Infof(ctx, "权限 %s/%s 已存在，跳过添加", permission.Owner, permission.Name)
				// 如果需要更新已存在的权限，可以在这里添加逻辑
				// 例如: 更新资源、操作等
				// casdoorsdk.UpdatePermission(permission)
			} else {
				// 权限不存在，添加新权限
				// g.Log().Infof(ctx, "权限 %s/%s 不存在，将添加新权限", permission.Owner, permission.Name)
				success, err := casdoorsdk.AddPermission(permission)
				if err != nil {
					g.Log().Errorf(ctx, "添加Casdoor权限 %s/%s 失败: %v", permission.Owner, permission.Name, err)
				} else if !success {
					g.Log().Errorf(ctx, "添加Casdoor权限 %s/%s 失败 (success=false)", permission.Owner, permission.Name)
				} else {
					// g.Log().Infof(ctx, "成功添加Casdoor权限: %s/%s", permission.Owner, permission.Name)
					// 添加成功后更新映射，以便同一批处理中的后续检查
					permissionMap[permission.Name] = permission
				}
			}
		}
		g.Log().Info(ctx, "Casdoor API权限同步完成")
	}
}
