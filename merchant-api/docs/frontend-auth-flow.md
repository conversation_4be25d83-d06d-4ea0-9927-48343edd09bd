# 前端认证流程文档

## 📋 概述

本文档详细描述了商户管理系统前端的完整认证流程，包括Casdoor单点登录、2FA双因子认证、Token管理和权限控制等核心环节。

## 🔄 完整认证流程

### 1. 初始登录阶段

#### 1.1 用户访问登录页面
- 用户打开商户管理系统
- 系统检查本地存储中是否有有效Token
- 如果没有Token或Token已过期，跳转到登录页面

#### 1.2 Casdoor单点登录
```javascript
// 跳转到Casdoor登录页面
const casdoorLoginUrl = `${CASDOOR_ENDPOINT}/login/oauth/authorize?` +
  `client_id=${CLIENT_ID}&` +
  `response_type=code&` +
  `redirect_uri=${REDIRECT_URI}&` +
  `scope=profile&` +
  `state=${generateRandomState()}`;

window.location.href = casdoorLoginUrl;
```

#### 1.3 Casdoor回调处理
```javascript
// 从URL参数获取授权码
const urlParams = new URLSearchParams(window.location.search);
const code = urlParams.get('code');
const state = urlParams.get('state');

// 验证state参数防止CSRF攻击
if (state !== localStorage.getItem('oauth_state')) {
  throw new Error('Invalid state parameter');
}

// 调用后端登录接口
const response = await fetch('/api/system/auth/casdoor/signin', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({ code, state })
});
```

### 2. 2FA认证阶段

#### 2.1 检查2FA状态
```javascript
const loginResult = await response.json();

if (loginResult.data.requireSetup2FA) {
  // 首次登录，需要设置2FA
  showSetup2FAPage(loginResult.data);
} else if (loginResult.data.require2FA) {
  // 已启用2FA，需要验证
  showVerify2FAPage(loginResult.data);
} else {
  // 直接登录成功（理论上不应该出现）
  handleLoginSuccess(loginResult.data);
}
```

#### 2.2 首次设置2FA流程
```javascript
function showSetup2FAPage(data) {
  // 显示2FA设置页面
  const setupPage = {
    title: '设置双因子认证',
    description: '为了保护您的账户安全，请设置双因子认证',
    qrCode: data.qrCodeURL,
    secret: data.totpSecret,
    merchantId: data.merchantId
  };
  
  // 渲染二维码
  renderQRCode(data.qrCodeURL);
  
  // 显示密钥文本（备用）
  showSecretKey(data.totpSecret);
  
  // 提供验证码输入框
  showVerificationInput();
}

async function handleSetup2FA(code, merchantId) {
  try {
    const response = await fetch('/api/system/auth/2fa/setup', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ code, merchantId })
    });
    
    const result = await response.json();
    
    if (result.code === 0) {
      // 显示恢复码
      showRecoveryCodes(result.data.recoveryCodes);
      
      // 用户确认保存恢复码后，完成登录
      handleLoginSuccess(result.data.token, result.data.expireAt);
    } else {
      showError('验证码错误，请重试');
    }
  } catch (error) {
    showError('设置失败，请重试');
  }
}
```

#### 2.3 2FA验证流程
```javascript
function showVerify2FAPage(data) {
  // 显示2FA验证页面
  const verifyPage = {
    title: '双因子认证',
    description: '请输入您的身份验证器中的6位验证码',
    merchantId: data.merchantId
  };
  
  // 显示验证码输入框
  showVerificationInput();
  
  // 提供"使用恢复码"选项
  showRecoveryCodeOption();
}

async function handleVerify2FA(code, merchantId) {
  try {
    const response = await fetch('/api/system/auth/2fa/verify', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ code, merchantId })
    });
    
    const result = await response.json();
    
    if (result.code === 0) {
      handleLoginSuccess(result.data.token, result.data.expireAt);
    } else if (result.code === 1017) {
      showError('尝试次数过多，账户已被锁定，请稍后重试');
    } else {
      showError('验证码错误，请重试');
    }
  } catch (error) {
    showError('验证失败，请重试');
  }
}
```

### 3. 登录成功处理

#### 3.1 Token存储和管理
```javascript
function handleLoginSuccess(token, expireAt) {
  // 存储Token到本地存储
  localStorage.setItem('access_token', token);
  localStorage.setItem('token_expire_at', expireAt);
  
  // 设置axios默认请求头
  axios.defaults.headers.common['Authorization'] = `Bearer ${token}`;
  
  // 清除登录相关的临时数据
  localStorage.removeItem('oauth_state');
  
  // 跳转到主页面
  window.location.href = '/dashboard';
}
```

#### 3.2 Token自动刷新机制
```javascript
// 设置Token过期检查
function setupTokenRefresh() {
  setInterval(() => {
    const expireAt = localStorage.getItem('token_expire_at');
    const now = Date.now() / 1000;
    
    // 提前5分钟刷新Token
    if (expireAt && (expireAt - now) < 300) {
      refreshToken();
    }
  }, 60000); // 每分钟检查一次
}

async function refreshToken() {
  try {
    const refreshToken = localStorage.getItem('refresh_token');
    const response = await fetch('/api/system/auth/refresh', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ refresh_token: refreshToken })
    });
    
    const result = await response.json();
    if (result.code === 0) {
      handleLoginSuccess(result.data.token, result.data.expireAt);
    } else {
      // 刷新失败，重新登录
      logout();
    }
  } catch (error) {
    logout();
  }
}
```

### 4. 请求拦截和权限控制

#### 4.1 HTTP请求拦截器
```javascript
// 请求拦截器
axios.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('access_token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// 响应拦截器
axios.interceptors.response.use(
  (response) => {
    return response;
  },
  (error) => {
    if (error.response?.status === 401) {
      // Token无效或过期，重新登录
      logout();
    } else if (error.response?.status === 403) {
      // 权限不足
      showError('您没有权限访问此功能');
    }
    return Promise.reject(error);
  }
);
```

#### 4.2 路由守卫
```javascript
// Vue Router守卫示例
router.beforeEach((to, from, next) => {
  const token = localStorage.getItem('access_token');
  const expireAt = localStorage.getItem('token_expire_at');
  const now = Date.now() / 1000;
  
  // 检查是否需要登录
  if (to.meta.requiresAuth) {
    if (!token || (expireAt && expireAt < now)) {
      // Token不存在或已过期
      next('/login');
    } else {
      // 检查页面权限
      if (hasPagePermission(to.path)) {
        next();
      } else {
        next('/403'); // 权限不足页面
      }
    }
  } else {
    next();
  }
});
```

### 5. 用户界面设计

#### 5.1 登录页面要素
- Casdoor登录按钮
- 系统Logo和标题
- 安全提示信息
- 语言切换选项

#### 5.2 2FA设置页面要素
- 二维码显示区域
- 密钥文本显示（可复制）
- 验证码输入框
- 设置说明和帮助链接
- 返回和继续按钮

#### 5.3 2FA验证页面要素
- 验证码输入框（6位数字）
- 倒计时显示
- "使用恢复码"链接
- 重新发送选项
- 帮助和支持链接

#### 5.4 恢复码显示页面
- 10个恢复码列表
- 下载/打印按钮
- 安全提示信息
- 确认保存按钮

### 6. 错误处理和用户体验

#### 6.1 错误信息处理
```javascript
function handleAuthError(errorCode, errorMessage) {
  const errorMap = {
    1013: '需要进行双因子认证',
    1014: '验证码无效，请重试',
    1015: '需要设置双因子认证',
    1016: '认证信息已过期，请重新登录',
    1017: '验证失败次数过多，账户已被锁定'
  };
  
  const message = errorMap[errorCode] || errorMessage || '认证失败';
  showNotification(message, 'error');
}
```

#### 6.2 加载状态管理
```javascript
// 显示加载状态
function showLoading(message = '正在处理...') {
  const loadingEl = document.getElementById('loading');
  loadingEl.textContent = message;
  loadingEl.style.display = 'block';
}

// 隐藏加载状态
function hideLoading() {
  document.getElementById('loading').style.display = 'none';
}
```

### 7. 安全最佳实践

#### 7.1 前端安全措施
- 敏感信息不存储在localStorage（如密钥）
- 使用HTTPS确保传输安全
- 实施CSP（内容安全策略）
- 定期清理过期的本地存储数据

#### 7.2 用户教育
- 提供2FA设置指南
- 强调恢复码的重要性
- 提供安全使用建议
- 设置密码强度要求

### 8. 移动端适配

#### 8.1 响应式设计
- 二维码在移动设备上的适配
- 验证码输入框的优化
- 触摸友好的按钮设计

#### 8.2 移动端特殊处理
- 支持相机扫描二维码
- 自动填充验证码（如果支持）
- 生物识别集成（指纹/面部识别）

## 🎯 总结

前端认证流程的核心是确保用户身份的安全验证，通过Casdoor单点登录和2FA双因子认证的组合，提供了强大的安全保障。同时，良好的用户体验设计确保了认证过程的流畅性和易用性。

关键要点：
1. **安全第一**：多层认证保护
2. **用户友好**：清晰的引导和反馈
3. **错误处理**：完善的异常情况处理
4. **状态管理**：合理的Token生命周期管理
5. **权限控制**：细粒度的访问控制
