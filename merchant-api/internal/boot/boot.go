// Package boot 包含应用程序的引导初始化逻辑
package boot

import (
	"context"
	"merchant-api/internal/casdoor"
	"merchant-api/internal/service"

	// "github.com/yalks/wallet"

	"github.com/gogf/gf/v2/os/glog"
)

// Initialize 初始化应用程序所有组件
func Initialize(ctx context.Context) {
	// 初始化日志输出
	glog.Info(ctx, "应用程序开始初始化...")

	// // 初始化系统配置（使用 XPay Config）
	// err := config.Initialize(ctx)
	// if err != nil {
	// 	glog.Errorf(ctx, "初始化系统配置失败: %v", err)
	// 	panic(err)
	// }

	// 初始化casdoor
	casdoor.InitCasdoor(ctx) // 初始化casdoor配置

	// 初始化商户 Casdoor 服务
	if err := service.InitMerchantCasdoorService(ctx); err != nil {
		glog.Errorf(ctx, "初始化商户 Casdoor 服务失败: %v", err)
		// 不要 panic，因为这不是关键服务
	}

	// //初始化钱包服务
	// if err := service.Wallet().Init(ctx); err != nil {
	// 	g.Log().Errorf(ctx, "Failed to initialize Wallet service: %v", err)
	// 	panic(err)
	// }

	//更换钱包初始逻辑

	// if err := wallet.Initialize(ctx); err != nil {
	// 	glog.Errorf(ctx, "Failed to initialize Wallet service: %v", err)
	// 	panic(err)
	// }

	glog.Info(ctx, "应用程序初始化完成")
}
