package v1

import (
	"merchant-api/api/common"
	"merchant-api/internal/model/entity"
	"time"

	"github.com/gogf/gf/v2/frame/g"
)

// --- IP访问列表查询 ---

// GetIpAccessListReq defines the request structure for querying the IP access list.
type GetIpAccessListReq struct {
	g.Meta `path:"/ip-access-lists" method:"get" tags:"SystemIpAccessList" summary:"查询IP访问列表"`
	common.PageRequest
	// IpAddress filters by IP address (supports fuzzy search).
	IpAddress string `json:"ipAddress"  dc:"IP地址，支持模糊查询"`
	// ListType filters by list type (blacklist or whitelist).
	ListType string `json:"listType"  dc:"列表类型，可选值：blacklist黑名单，whitelist白名单"`
	// IsEnabled filters by enabled status (1: enabled, 0: disabled, -1: all).
	IsEnabled int      `json:"isEnabled"  d:"-1" dc:"是否启用：1启用，0禁用，-1全部"`
	DateRange string   `json:"dateRange" dc:"创建时间范围，格式：2025-01-01,2025-01-31"`
	CreatedAt []string `json:"createdAt" dc:"创建时间范围数组，格式：[开始时间, 结束时间]"`
	// Export indicates whether to export the results (0: no, 1: yes).
	Export int `json:"export" d:"0" dc:"是否导出：0不导出，1导出"`
}

// GetIpAccessListRes defines the response structure for the IP access list query.
type GetIpAccessListRes struct {
	// Page contains the pagination information.
	Page common.PageResponse `json:"page" dc:"分页信息"`
	// Data contains the list of IP access list entries.
	Data []*entity.IpAccessList `json:"data" dc:"IP访问列表数据"`
}

// --- 添加IP到访问列表 ---

// AddIpAccessListReq defines the request structure for adding an IP to the access list.
type AddIpAccessListReq struct {
	g.Meta `path:"/ip-access-lists" method:"post" tags:"SystemIpAccessList" summary:"添加IP到访问列表"`
	// ListType specifies the type of list (blacklist or whitelist).
	ListType string `json:"listType"  v:"required#列表类型不能为空" dc:"列表类型，可选值：blacklist黑名单，whitelist白名单"`
	// IpAddress is the IP address to add.
	IpAddress string `json:"ipAddress"  v:"required|ip#IP地址不能为空|IP地址格式不正确" dc:"IP地址"`
	// Reason provides an optional reason for adding the IP.
	Reason string `json:"reason" dc:"原因"`
	// ExpiresAt sets an optional expiration time for the entry (nil for permanent).
	ExpiresAt *time.Time `json:"expiresAt" dc:"过期时间，为空表示永久"`
}

// AddIpAccessListRes defines the response structure after adding an IP to the access list.
type AddIpAccessListRes struct {
	// Id is the ID of the newly created access list entry.
	Id int64 `json:"id" dc:"新增记录ID"`
}

// --- 从访问列表删除IP ---

// DeleteIpAccessListReq defines the request structure for deleting an IP from the access list.
type DeleteIpAccessListReq struct {
	g.Meta `path:"/ip-access-lists/{id}" method:"delete" tags:"SystemIpAccessList" summary:"从访问列表删除IP"`
	// Id is the ID of the access list entry to delete (obtained from path).
	Id int64 `json:"id" v:"required#记录ID不能为空" dc:"记录ID"`
}

// DeleteIpAccessListRes defines the response structure after deleting an IP from the access list.
type DeleteIpAccessListRes struct {
	// Id is the ID of the deleted access list entry.
	Id int64 `json:"id" dc:"删除的记录ID"`
}

// --- 更新IP访问控制状态 ---

// PatchIpAccessListReq defines the request structure for updating the status of an IP access list entry.
type PatchIpAccessListReq struct {
	g.Meta `path:"/ip-access-lists/{id}" method:"patch" tags:"SystemIpAccessList" summary:"更新IP访问控制状态"`
	// Id is the ID of the access list entry to update (obtained from path).
	Id int64 `json:"id" v:"required#记录ID不能为空" dc:"记录ID"`
	// IsEnabled sets the desired status (1: enabled, 0: disabled). Pointer allows partial update.
	IsEnabled *int `json:"isEnabled" v:"required|in:0,1#状态值必须是0或1" dc:"是否启用：1启用，0禁用"`
}

// PatchIpAccessListRes defines the response structure after updating the status of an IP access list entry.
type PatchIpAccessListRes struct {
	// Id is the ID of the updated access list entry.
	Id int64 `json:"id" dc:"更新的记录ID"`
}
