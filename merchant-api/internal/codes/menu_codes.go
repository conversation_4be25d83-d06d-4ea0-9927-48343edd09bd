package codes

import (
	"github.com/gogf/gf/v2/errors/gcode"
)

// 菜单管理模块错误码 (3200-3299)
var (
	// CodeMenuNotFound 菜单不存在
	CodeMenuNotFound = gcode.New(3201, "菜单不存在", nil)
	// CodeMenuNameExists 菜单名称已存在
	CodeMenuNameExists = gcode.New(3202, "菜单名称已存在", nil)
	// CodeMenuHasChildren 菜单存在子菜单，无法删除
	CodeMenuHasChildren = gcode.New(3203, "菜单存在子菜单或按钮，无法删除", nil)
	// CodeMenuParentNotFound 指定的上级菜单不存在
	CodeMenuParentNotFound = gcode.New(3204, "指定的上级菜单不存在", nil)
	// CodeMenuCannotSetParentToChild 不能将上级菜单设置为自身或其子孙菜单
	CodeMenuCannotSetParentToChild = gcode.New(3205, "不能将上级菜单设置为自身或其子孙菜单", nil)
	// 移除与类型相关的错误码
	// CodeMenuInvalidType = gcode.New(3206, "无效的菜单类型", nil)
	// CodeMenuParentTypeInvalid = gcode.New(3207, "上级菜单类型错误", nil)
	// CodeMenuCreateFailed 菜单创建失败
	CodeMenuCreateFailed = gcode.New(3208, "菜单创建失败", nil)
)
