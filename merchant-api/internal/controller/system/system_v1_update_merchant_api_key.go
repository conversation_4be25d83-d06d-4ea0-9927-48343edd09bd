package system

import (
	"context"

	"merchant-api/internal/service"

	"github.com/gogf/gf/v2/net/ghttp"

	v1 "merchant-api/api/system/v1"
)

// UpdateMerchantApiKey implements the logic for the UpdateMerchantApiKey controller.
func (c *ControllerV1) UpdateMerchantApiKey(ctx context.Context, req *v1.UpdateMerchantApiKeyReq) (res *v1.UpdateMerchantApiKeyRes, err error) {
	// 从路径参数中获取 merchantId 和 apiKeyId
	r := ghttp.RequestFromCtx(ctx)
	req.MerchantId = r.Get("merchantId").Uint()
	req.ApiKeyId = r.Get("apiKeyId").Uint()
	res, err = service.SystemServiceInstance.UpdateMerchantApiKey(ctx, req)
	return
}
