package v1

import (
	"merchant-api/api/common"

	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
	"github.com/shopspring/decimal"
)

// --- 获取我的资金记录列表 ---

// GetMyTransactionsReq defines the request structure for getting merchant's own transaction records.
type GetMyTransactionsReq struct {
	g.Meta `path:"/my/transactions" method:"get" tags:"MerchantTransactions" summary:"获取我的资金记录"`
	common.PageRequest
	
	// 筛选条件
	TokenSymbol   string `json:"tokenSymbol" dc:"代币符号筛选 (如: USDT, BTC, ETH)"`
	Type          string `json:"type" dc:"交易类型筛选 (如: deposit, withdrawal, transfer, system_adjust)"`
	Direction     string `json:"direction" dc:"资金方向筛选 (in-增加, out-减少)"`
	WalletType    string `json:"walletType" dc:"钱包类型筛选 (available-可用余额, frozen-冻结余额)"`
	Status        *uint    `json:"status" dc:"交易状态筛选 (1-成功, 0-失败)"`
	DateRange     string   `json:"dateRange" dc:"创建时间范围，格式：2025-01-01,2025-01-31"`
	CreatedAt     []string `json:"createdAt" dc:"创建时间范围数组，格式：[开始时间, 结束时间]"`
	AmountMin     string   `json:"amountMin" dc:"最小金额"`
	AmountMax     string `json:"amountMax" dc:"最大金额"`
	BusinessId    string `json:"businessId" dc:"业务ID搜索"`
	RelatedEntityType string `json:"relatedEntityType" dc:"关联实体类型筛选"`
	
	// 导出功能
	Export        bool   `json:"export" dc:"是否导出 (true-导出, false-不导出)"`
}

// MerchantTransactionInfoType defines the structure for transaction information in a list.
type MerchantTransactionInfoType struct {
	TransactionId    uint64          `json:"transactionId" dc:"交易记录ID"`
	Type            string          `json:"type" dc:"交易类型"`
	TypeText        string          `json:"typeText" dc:"交易类型描述"`
	WalletType      string          `json:"walletType" dc:"钱包类型"`
	WalletTypeText  string          `json:"walletTypeText" dc:"钱包类型描述"`
	Direction       string          `json:"direction" dc:"资金方向"`
	DirectionText   string          `json:"directionText" dc:"资金方向描述"`
	Amount          decimal.Decimal `json:"amount" dc:"交易金额"`
	BalanceBefore   decimal.Decimal `json:"balanceBefore" dc:"交易前余额"`
	BalanceAfter    decimal.Decimal `json:"balanceAfter" dc:"交易后余额"`
	Symbol          string          `json:"symbol" dc:"代币符号"`
	Status          uint            `json:"status" dc:"交易状态"`
	StatusText      string          `json:"statusText" dc:"交易状态描述"`
	Memo            string          `json:"memo" dc:"交易备注"`
	CreatedAt       *gtime.Time     `json:"createdAt" dc:"创建时间"`
	ProcessedAt     *gtime.Time     `json:"processedAt" dc:"处理时间"`
	
	// 关联信息
	RelatedEntityType string `json:"relatedEntityType,omitempty" dc:"关联实体类型"`
	RelatedEntityId   uint64 `json:"relatedEntityId,omitempty" dc:"关联实体ID"`
	BusinessId        string `json:"businessId,omitempty" dc:"业务ID"`
	
	// 请求信息
	RequestSource    string          `json:"requestSource,omitempty" dc:"请求来源"`
	RequestReference string          `json:"requestReference,omitempty" dc:"请求参考信息"`
	RequestAmount    decimal.Decimal `json:"requestAmount,omitempty" dc:"请求原始金额"`
	FeeAmount        decimal.Decimal `json:"feeAmount,omitempty" dc:"手续费金额"`
}

// GetMyTransactionsRes defines the response structure for getting merchant's own transaction records.
type GetMyTransactionsRes struct {
	Page common.PageResponse              `json:"page" dc:"分页信息"`
	Data []*MerchantTransactionInfoType   `json:"data" dc:"资金记录列表"`
}

// --- 获取我的资金记录详情 ---

// GetMyTransactionDetailReq defines the request structure for getting transaction detail.
type GetMyTransactionDetailReq struct {
	g.Meta        `path:"/my/transactions/{transactionId}" method:"get" tags:"MerchantTransactions" summary:"获取我的资金记录详情"`
	TransactionId uint64 `json:"transactionId" v:"required" dc:"交易记录ID"`
}

// MerchantTransactionDetailType defines the structure for detailed transaction information.
type MerchantTransactionDetailType struct {
	TransactionId        uint64          `json:"transactionId" dc:"交易记录ID"`
	MerchantId           uint            `json:"merchantId" dc:"商户ID"`
	TokenId              uint            `json:"tokenId" dc:"代币ID"`
	Type                 string          `json:"type" dc:"交易类型"`
	TypeText             string          `json:"typeText" dc:"交易类型描述"`
	WalletType           string          `json:"walletType" dc:"钱包类型"`
	WalletTypeText       string          `json:"walletTypeText" dc:"钱包类型描述"`
	Direction            string          `json:"direction" dc:"资金方向"`
	DirectionText        string          `json:"directionText" dc:"资金方向描述"`
	Amount               decimal.Decimal `json:"amount" dc:"交易金额"`
	BalanceBefore        decimal.Decimal `json:"balanceBefore" dc:"交易前余额"`
	BalanceAfter         decimal.Decimal `json:"balanceAfter" dc:"交易后余额"`
	Symbol               string          `json:"symbol" dc:"代币符号"`
	Status               uint            `json:"status" dc:"交易状态"`
	StatusText           string          `json:"statusText" dc:"交易状态描述"`
	Memo                 string          `json:"memo" dc:"交易备注"`
	
	// 关联信息
	RelatedTransactionId uint64 `json:"relatedTransactionId,omitempty" dc:"关联交易ID"`
	RelatedEntityId      uint64 `json:"relatedEntityId,omitempty" dc:"关联实体ID"`
	RelatedEntityType    string `json:"relatedEntityType,omitempty" dc:"关联实体类型"`
	BusinessId           string `json:"businessId" dc:"业务ID"`
	
	// 请求详细信息
	RequestAmount        decimal.Decimal `json:"requestAmount,omitempty" dc:"用户请求原始金额"`
	RequestReference     string          `json:"requestReference,omitempty" dc:"用户请求参考信息"`
	RequestSource        string          `json:"requestSource,omitempty" dc:"请求来源"`
	RequestIp            string          `json:"requestIp,omitempty" dc:"请求IP地址"`
	RequestUserAgent     string          `json:"requestUserAgent,omitempty" dc:"请求User-Agent"`
	
	// 费用信息
	FeeAmount            decimal.Decimal `json:"feeAmount,omitempty" dc:"手续费金额"`
	FeeType              string          `json:"feeType,omitempty" dc:"手续费类型"`
	ExchangeRate         decimal.Decimal `json:"exchangeRate,omitempty" dc:"汇率"`
	
	// 目标信息（转账等）
	TargetUserId         uint            `json:"targetUserId,omitempty" dc:"目标用户ID"`
	TargetUsername       string          `json:"targetUsername,omitempty" dc:"目标用户名"`
	
	// 时间信息
	CreatedAt            *gtime.Time     `json:"createdAt" dc:"创建时间"`
	UpdatedAt            *gtime.Time     `json:"updatedAt" dc:"更新时间"`
	RequestTimestamp     *gtime.Time     `json:"requestTimestamp,omitempty" dc:"请求时间戳"`
	ProcessedAt          *gtime.Time     `json:"processedAt,omitempty" dc:"处理完成时间"`
}

// GetMyTransactionDetailRes defines the response structure for getting transaction detail.
type GetMyTransactionDetailRes struct {
	Data *MerchantTransactionDetailType `json:"data" dc:"资金记录详情"`
}

// --- 获取我的资金统计信息 ---

// GetMyTransactionStatsReq defines the request structure for getting transaction statistics.
type GetMyTransactionStatsReq struct {
	g.Meta      `path:"/my/transactions/stats" method:"get" tags:"MerchantTransactions" summary:"获取我的资金统计信息"`
	TokenSymbol string `json:"tokenSymbol" dc:"代币符号 (为空时统计所有币种)"`
	DateRange   string `json:"dateRange" dc:"统计时间范围，格式：2025-01-01,2025-01-31"`
	Type        string `json:"type" dc:"交易类型筛选"`
}

// TransactionStatsType defines the structure for transaction statistics.
type TransactionStatsType struct {
	// 总体统计
	TotalTransactions    int64           `json:"totalTransactions" dc:"总交易笔数"`
	SuccessTransactions  int64           `json:"successTransactions" dc:"成功交易笔数"`
	FailedTransactions   int64           `json:"failedTransactions" dc:"失败交易笔数"`
	SuccessRate          string          `json:"successRate" dc:"成功率"`
	
	// 金额统计
	TotalAmountIn        decimal.Decimal `json:"totalAmountIn" dc:"总入账金额"`
	TotalAmountOut       decimal.Decimal `json:"totalAmountOut" dc:"总出账金额"`
	NetAmount            decimal.Decimal `json:"netAmount" dc:"净变动金额"`
	TotalFeeAmount       decimal.Decimal `json:"totalFeeAmount" dc:"总手续费"`
	
	// 按类型分组统计
	TypeStats            map[string]*TransactionTypeStatsType `json:"typeStats" dc:"按交易类型统计"`
	
	// 币种统计（当查询所有币种时）
	TokenStats           map[string]*TransactionTokenStatsType `json:"tokenStats,omitempty" dc:"按币种统计"`
	
	// 时间范围
	DateRange            string          `json:"dateRange" dc:"统计时间范围"`
	LastUpdated          *gtime.Time     `json:"lastUpdated" dc:"最后更新时间"`
}

// TransactionTypeStatsType defines statistics by transaction type.
type TransactionTypeStatsType struct {
	Type          string          `json:"type" dc:"交易类型"`
	TypeText      string          `json:"typeText" dc:"交易类型描述"`
	Count         int64           `json:"count" dc:"交易笔数"`
	TotalAmount   decimal.Decimal `json:"totalAmount" dc:"总金额"`
	SuccessCount  int64           `json:"successCount" dc:"成功笔数"`
	FailedCount   int64           `json:"failedCount" dc:"失败笔数"`
}

// TransactionTokenStatsType defines statistics by token.
type TransactionTokenStatsType struct {
	TokenSymbol     string          `json:"tokenSymbol" dc:"代币符号"`
	Count           int64           `json:"count" dc:"交易笔数"`
	TotalAmountIn   decimal.Decimal `json:"totalAmountIn" dc:"总入账"`
	TotalAmountOut  decimal.Decimal `json:"totalAmountOut" dc:"总出账"`
	NetAmount       decimal.Decimal `json:"netAmount" dc:"净变动"`
	CurrentBalance  decimal.Decimal `json:"currentBalance,omitempty" dc:"当前余额"`
}

// GetMyTransactionStatsRes defines the response structure for getting transaction statistics.
type GetMyTransactionStatsRes struct {
	Data *TransactionStatsType `json:"data" dc:"资金统计信息"`
}