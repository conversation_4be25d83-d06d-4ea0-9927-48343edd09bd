package common

import (
	"context"

	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gcfg"

	"merchant-api/internal/boot"
)

// InitializeConfig 初始化配置系统
func InitializeConfig(ctx context.Context) error {
	g.Log().Info(ctx, "Initializing configuration system...")

	// 显式设置配置文件加载路径
	// 注意：这假设 GoFrame 默认会加载该目录下的 config.yaml 文件
	// 如果有更复杂的加载逻辑（如基于环境），可能需要调整
	if adapterFile, ok := g.Cfg().GetAdapter().(*gcfg.AdapterFile); ok {
		// 检查 SetPath 方法是否存在并调用
		// 注意：直接调用 SetPath 可能在某些 GoFrame 版本中不可用或行为不同
		// 推荐的方式通常是在启动时通过命令行参数 -gf.gcfg.file 指定配置文件
		// 或者通过环境变量 GF_GCFG_PATH 设置路径
		// 这里我们尝试 SetPath，如果失败或不可用，日志会提示
		if err := adapterFile.SetPath("manifest/config"); err != nil {
			// 如果 SetPath 失败，记录警告而不是致命错误，因为 GoFrame 可能已经通过其他方式加载了配置
			g.Log().Warningf(ctx, "Attempt to set configuration path via SetPath failed (may not be supported or necessary): %v", err)
		} else {
			g.Log().Info(ctx, "Attempted to set configuration path to 'manifest/config' via SetPath.")
		}
		// 强制重新加载配置，确保设置生效
		// 注意：Reload 可能有性能影响，且不一定总是需要
		// g.Cfg().Reload()
		// g.Log().Info(ctx, "Configuration reloaded after setting path.")
	} else {
		g.Log().Warning(ctx, "Configuration adapter is not *gcfg.AdapterFile, cannot set path explicitly.")
		// 根据实际情况决定是否需要 Fatalf
	}

	g.Log().Info(ctx, "Configuration system initialized successfully.")
	return nil
}

// InitializeServices 初始化所有必要的服务
func InitializeServices(ctx context.Context) error {
	g.Log().Info(ctx, "Initializing core services...")

	// 首先调用系统级引导初始化（包含配置和钱包管理器）
	boot.Initialize(ctx)

	g.Log().Info(ctx, "Core services initialized successfully.")
	return nil
}
